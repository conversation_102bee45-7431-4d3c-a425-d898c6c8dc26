"""
路径规划算法基础接口和数据结构
用于海冰分割系统的路径规划功能
"""

import numpy as np
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import List, Tuple, Dict, Optional, Any
import matplotlib.pyplot as plt
from scipy.spatial import cKDTree
import cv2


@dataclass
class PlanningResult:
    """路径规划结果的标准数据结构"""
    path: Optional[List[Tuple[int, int]]]  # 路径点列表 (row, col)，None表示未找到路径
    success: bool                          # 是否成功找到路径
    cost: float                           # 路径代价
    computation_time: float               # 计算时间（秒）
    iterations: int                       # 迭代次数
    algorithm_name: str                   # 算法名称
    additional_data: Dict[str, Any] = field(default_factory=dict)  # 算法特定的额外数据
    
    def __post_init__(self):
        """后处理：确保数据一致性"""
        if self.path is None or len(self.path) == 0:
            self.success = False
            self.cost = float('inf')
        elif not self.success:
            self.success = True  # 如果有路径但success为False，自动修正


@dataclass
class AlgorithmParams:
    """算法参数的标准结构"""
    max_iterations: int = 1000
    step_size: float = 1.0
    goal_bias: float = 0.1
    timeout: float = 30.0  # 超时时间（秒）
    # 算法特定参数
    algorithm_specific: Dict[str, Any] = field(default_factory=dict)


class PathPlannerBase(ABC):
    """路径规划算法基类"""
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], 
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化路径规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图，0表示可通行，1表示障碍物
            params: 算法参数
        """
        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map, dtype=int)
        self.map_height, self.map_width = self.grid_map.shape
        self.params = params or AlgorithmParams()
        
        # 验证输入
        self._validate_inputs()
        
        # 算法状态
        self.start_time = None
        self.iterations = 0
        
    def _validate_inputs(self):
        """验证输入参数"""
        # 检查起点和终点是否在地图范围内
        if not (0 <= self.start[0] < self.map_height and 0 <= self.start[1] < self.map_width):
            raise ValueError(f"起点 {self.start} 超出地图范围 ({self.map_height}, {self.map_width})")
        
        if not (0 <= self.goal[0] < self.map_height and 0 <= self.goal[1] < self.map_width):
            raise ValueError(f"终点 {self.goal} 超出地图范围 ({self.map_height}, {self.map_width})")
        
        # 检查起点和终点是否在障碍物上
        if self.grid_map[self.start[0], self.start[1]] == 1:
            raise ValueError(f"起点 {self.start} 位于障碍物上")
        
        if self.grid_map[self.goal[0], self.goal[1]] == 1:
            raise ValueError(f"终点 {self.goal} 位于障碍物上")
    
    @abstractmethod
    def plan(self) -> PlanningResult:
        """
        执行路径规划
        
        Returns:
            PlanningResult: 规划结果
        """
        pass
    
    @abstractmethod
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        pass
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取算法性能指标"""
        return {
            'iterations': self.iterations,
            'map_size': self.grid_map.shape,
            'start': self.start,
            'goal': self.goal
        }
    
    def visualize(self, result: PlanningResult, show_process: bool = False, 
                  save_path: Optional[str] = None):
        """
        可视化结果
        
        Args:
            result: 规划结果
            show_process: 是否显示算法过程
            save_path: 保存路径，None表示不保存
        """
        plt.figure(figsize=(10, 8))
        
        # 显示地图
        plt.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制路径
        if result.success and result.path:
            path_array = np.array(result.path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=3, label='Path')
            plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=4)
        
        # 标记起点和终点
        plt.plot(self.start[1], self.start[0], 'go', markersize=12, label='Start')
        plt.plot(self.goal[1], self.goal[0], 'ro', markersize=12, label='Goal')
        
        # 设置标题和标签
        status = "成功" if result.success else "失败"
        title = f'{result.algorithm_name} - {status}\n'
        title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s, 迭代: {result.iterations}'
        plt.title(title, fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


class PathPlanningUtils:
    """路径规划工具函数集合"""
    
    @staticmethod
    def is_valid_point(point: Tuple[int, int], grid_map: np.ndarray) -> bool:
        """
        检查点是否有效（在地图范围内且不是障碍物）
        
        Args:
            point: 点坐标 (row, col)
            grid_map: 网格地图
            
        Returns:
            bool: 是否有效
        """
        row, col = point
        height, width = grid_map.shape
        
        # 检查边界
        if row < 0 or row >= height or col < 0 or col >= width:
            return False
        
        # 检查障碍物
        return grid_map[row, col] == 0
    
    @staticmethod
    def check_collision_line(start: Tuple[int, int], end: Tuple[int, int], 
                           grid_map: np.ndarray, step_size: float = 0.5) -> bool:
        """
        检查线段是否与障碍物碰撞
        
        Args:
            start: 起点 (row, col)
            end: 终点 (row, col)
            grid_map: 网格地图
            step_size: 检查步长
            
        Returns:
            bool: True表示有碰撞，False表示无碰撞
        """
        start_row, start_col = start
        end_row, end_col = end
        
        # 计算距离和步数
        distance = np.sqrt((end_row - start_row)**2 + (end_col - start_col)**2)
        if distance == 0:
            return False
        
        num_steps = int(distance / step_size) + 1
        
        # 沿线段检查每个点
        for i in range(num_steps + 1):
            t = i / num_steps if num_steps > 0 else 0
            check_row = int(start_row + t * (end_row - start_row))
            check_col = int(start_col + t * (end_col - start_col))
            
            if not PathPlanningUtils.is_valid_point((check_row, check_col), grid_map):
                return True
        
        return False
    
    @staticmethod
    def calculate_path_metrics(path: List[Tuple[int, int]], 
                             grid_map: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        计算路径指标
        
        Args:
            path: 路径点列表
            grid_map: 网格地图（可选，用于计算风险指标）
            
        Returns:
            Dict: 路径指标
        """
        if not path or len(path) < 2:
            return {
                'length': 0.0,
                'euclidean_length': 0.0,
                'smoothness': 0.0,
                'turn_count': 0
            }
        
        # 计算欧几里得长度
        euclidean_length = 0.0
        for i in range(len(path) - 1):
            p1, p2 = path[i], path[i + 1]
            euclidean_length += np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
        
        # 计算转弯次数
        turn_count = 0
        if len(path) >= 3:
            for i in range(1, len(path) - 1):
                p1, p2, p3 = path[i-1], path[i], path[i+1]
                # 计算方向向量
                v1 = (p2[0] - p1[0], p2[1] - p1[1])
                v2 = (p3[0] - p2[0], p3[1] - p2[1])
                # 如果方向改变，计为一次转弯
                if v1 != v2:
                    turn_count += 1
        
        # 计算平滑度（角度变化总和）
        smoothness = 0.0
        if len(path) >= 3:
            for i in range(1, len(path) - 1):
                p1, p2, p3 = path[i-1], path[i], path[i+1]
                v1 = np.array([p2[0] - p1[0], p2[1] - p1[1]])
                v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])
                
                # 计算角度变化
                if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
                    angle_change = np.arccos(cos_angle)
                    smoothness += angle_change
        
        return {
            'length': len(path),
            'euclidean_length': euclidean_length,
            'smoothness': smoothness,
            'turn_count': turn_count
        }
    
    @staticmethod
    def smooth_path(path: List[Tuple[int, int]], grid_map: np.ndarray, 
                   max_iterations: int = 10) -> List[Tuple[int, int]]:
        """
        路径平滑处理
        
        Args:
            path: 原始路径
            grid_map: 网格地图
            max_iterations: 最大平滑迭代次数
            
        Returns:
            List: 平滑后的路径
        """
        if len(path) <= 2:
            return path
        
        smoothed_path = path.copy()
        
        for _ in range(max_iterations):
            improved = False
            i = 0
            
            while i < len(smoothed_path) - 2:
                # 尝试连接 i 和 i+2 点
                if not PathPlanningUtils.check_collision_line(
                    smoothed_path[i], smoothed_path[i + 2], grid_map):
                    # 可以直接连接，移除中间点
                    smoothed_path.pop(i + 1)
                    improved = True
                else:
                    i += 1
            
            if not improved:
                break
        
        return smoothed_path
    
    @staticmethod
    def distance(p1: Tuple[int, int], p2: Tuple[int, int]) -> float:
        """计算两点间的欧几里得距离"""
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    @staticmethod
    def find_nearest_point(target: Tuple[int, int], 
                          points: List[Tuple[int, int]]) -> Tuple[int, Tuple[int, int]]:
        """
        找到最近的点
        
        Args:
            target: 目标点
            points: 候选点列表
            
        Returns:
            Tuple: (索引, 最近点)
        """
        if not points:
            raise ValueError("候选点列表不能为空")
        
        min_dist = float('inf')
        nearest_idx = 0
        
        for i, point in enumerate(points):
            dist = PathPlanningUtils.distance(target, point)
            if dist < min_dist:
                min_dist = dist
                nearest_idx = i
        
        return nearest_idx, points[nearest_idx]
    
    @staticmethod
    def get_neighbors(point: Tuple[int, int], grid_map: np.ndarray, 
                     include_diagonal: bool = True) -> List[Tuple[int, int]]:
        """
        获取点的有效邻居
        
        Args:
            point: 中心点
            grid_map: 网格地图
            include_diagonal: 是否包含对角线邻居
            
        Returns:
            List: 有效邻居列表
        """
        row, col = point
        neighbors = []
        
        # 定义移动方向
        if include_diagonal:
            directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                         (0, 1), (1, -1), (1, 0), (1, 1)]
        else:
            directions = [(-1, 0), (0, -1), (0, 1), (1, 0)]
        
        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if PathPlanningUtils.is_valid_point((new_row, new_col), grid_map):
                neighbors.append((new_row, new_col))
        
        return neighbors


class PathPlanningVisualizer:
    """路径规划可视化工具"""
    
    @staticmethod
    def plot_result(result: PlanningResult, grid_map: np.ndarray, 
                   show_process: bool = False, save_path: Optional[str] = None):
        """
        统一的结果可视化
        
        Args:
            result: 规划结果
            grid_map: 网格地图
            show_process: 是否显示算法过程
            save_path: 保存路径
        """
        plt.figure(figsize=(10, 8))
        
        # 显示地图
        plt.imshow(grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制路径
        if result.success and result.path:
            path_array = np.array(result.path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=3, label='Path')
            plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=4)
        
        # 从结果中获取起点和终点
        if result.path and len(result.path) > 0:
            start = result.path[0]
            goal = result.path[-1]
            plt.plot(start[1], start[0], 'go', markersize=12, label='Start')
            plt.plot(goal[1], goal[0], 'ro', markersize=12, label='Goal')
        
        # 设置标题
        status = "成功" if result.success else "失败"
        title = f'{result.algorithm_name} - {status}\n'
        title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s, 迭代: {result.iterations}'
        plt.title(title, fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    @staticmethod
    def compare_algorithms(results: List[PlanningResult], grid_map: np.ndarray,
                          save_path: Optional[str] = None):
        """
        算法比较可视化
        
        Args:
            results: 多个算法的结果
            grid_map: 网格地图
            save_path: 保存路径
        """
        n_algorithms = len(results)
        if n_algorithms == 0:
            return
        
        # 计算子图布局
        cols = min(3, n_algorithms)
        rows = (n_algorithms + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if n_algorithms == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
        
        for i, result in enumerate(results):
            row = i // cols
            col = i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            # 显示地图
            ax.imshow(grid_map, cmap='gray_r', origin='upper', alpha=0.7)
            
            # 绘制路径
            if result.success and result.path:
                path_array = np.array(result.path)
                color = colors[i % len(colors)]
                ax.plot(path_array[:, 1], path_array[:, 0], color=color, 
                       linewidth=2, label='Path')
                ax.plot(path_array[:, 1], path_array[:, 0], 'o', color=color, markersize=3)
                
                # 标记起点和终点
                ax.plot(result.path[0][1], result.path[0][0], 'go', markersize=8, label='Start')
                ax.plot(result.path[-1][1], result.path[-1][0], 'ro', markersize=8, label='Goal')
            
            # 设置标题
            status = "成功" if result.success else "失败"
            title = f'{result.algorithm_name} - {status}\n'
            title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s'
            ax.set_title(title, fontsize=10)
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(n_algorithms, rows * cols):
            row = i // cols
            col = i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            ax.set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    @staticmethod
    def plot_metrics(results: List[PlanningResult], save_path: Optional[str] = None):
        """
        性能指标可视化
        
        Args:
            results: 算法结果列表
            save_path: 保存路径
        """
        if not results:
            return
        
        # 提取数据
        algorithms = [r.algorithm_name for r in results]
        costs = [r.cost if r.success else float('inf') for r in results]
        times = [r.computation_time for r in results]
        iterations = [r.iterations for r in results]
        success_rates = [1 if r.success else 0 for r in results]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 路径代价对比
        axes[0, 0].bar(algorithms, costs)
        axes[0, 0].set_title('路径代价对比')
        axes[0, 0].set_ylabel('代价')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 计算时间对比
        axes[0, 1].bar(algorithms, times)
        axes[0, 1].set_title('计算时间对比')
        axes[0, 1].set_ylabel('时间 (秒)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 迭代次数对比
        axes[1, 0].bar(algorithms, iterations)
        axes[1, 0].set_title('迭代次数对比')
        axes[1, 0].set_ylabel('迭代次数')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 成功率对比
        axes[1, 1].bar(algorithms, success_rates)
        axes[1, 1].set_title('成功率对比')
        axes[1, 1].set_ylabel('成功率')
        axes[1, 1].set_ylim(0, 1.1)
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


# 测试函数
def test_base_functionality():
    """测试基础功能"""
    print("🧪 测试基础接口和数据结构...")
    
    # 创建测试地图
    test_map = np.zeros((10, 10))
    test_map[3:7, 3:7] = 1  # 添加障碍物
    
    start = (1, 1)
    goal = (8, 8)
    
    # 测试工具函数
    print("✅ 测试工具函数...")
    
    # 测试点有效性检查
    assert PathPlanningUtils.is_valid_point((1, 1), test_map) == True
    assert PathPlanningUtils.is_valid_point((4, 4), test_map) == False
    assert PathPlanningUtils.is_valid_point((-1, 1), test_map) == False
    
    # 测试碰撞检测
    collision = PathPlanningUtils.check_collision_line((1, 1), (2, 2), test_map)
    assert collision == False
    
    collision = PathPlanningUtils.check_collision_line((1, 1), (5, 5), test_map)
    assert collision == True
    
    # 测试路径指标计算
    test_path = [(1, 1), (2, 2), (3, 3), (4, 4)]
    metrics = PathPlanningUtils.calculate_path_metrics(test_path)
    assert metrics['length'] == 4
    assert metrics['euclidean_length'] > 0
    
    print("✅ 基础功能测试通过！")
    
    # 测试数据结构
    result = PlanningResult(
        path=test_path,
        success=True,
        cost=10.0,
        computation_time=0.5,
        iterations=100,
        algorithm_name="Test"
    )
    
    assert result.success == True
    assert len(result.path) == 4
    
    print("✅ 数据结构测试通过！")
    print("🎉 所有基础功能测试完成！")


if __name__ == "__main__":
    test_base_functionality()