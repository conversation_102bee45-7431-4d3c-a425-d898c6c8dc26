"""
重构后的粒子群优化(PSO)路径规划算法
适用于海冰分割系统的网格地图环境
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt

from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils


class PSOParticle:
    """PSO粒子类"""
    def __init__(self, waypoints: np.ndarray):
        """
        初始化粒子
        
        Args:
            waypoints: 路径关键点 shape: (n_waypoints, 2)
        """
        self.position = waypoints.copy()  # 当前位置（路径关键点）
        self.velocity = np.zeros_like(waypoints)  # 速度
        self.best_position = waypoints.copy()  # 个体最优位置
        self.best_fitness = float('inf')  # 个体最优适应度
        self.current_fitness = float('inf')  # 当前适应度
        
    def update_velocity(self, global_best: np.ndarray, w: float, c1: float, c2: float):
        """
        更新粒子速度
        
        Args:
            global_best: 全局最优位置
            w: 惯性权重
            c1: 个体学习因子
            c2: 社会学习因子
        """
        r1 = np.random.random(self.position.shape)
        r2 = np.random.random(self.position.shape)
        
        # PSO速度更新公式
        self.velocity = (w * self.velocity + 
                        c1 * r1 * (self.best_position - self.position) +
                        c2 * r2 * (global_best - self.position))
    
    def update_position(self, bounds: Tuple[Tuple[float, float], Tuple[float, float]]):
        """
        更新粒子位置并应用边界约束
        
        Args:
            bounds: 位置边界 ((min_row, max_row), (min_col, max_col))
        """
        # 更新位置（保持起点和终点不变）
        self.position[1:-1] += self.velocity[1:-1]
        
        # 应用边界约束
        (min_row, max_row), (min_col, max_col) = bounds
        self.position[:, 0] = np.clip(self.position[:, 0], min_row, max_row)
        self.position[:, 1] = np.clip(self.position[:, 1], min_col, max_col)
    
    def update_best(self):
        """更新个体最优"""
        if self.current_fitness < self.best_fitness:
            self.best_fitness = self.current_fitness
            self.best_position = self.position.copy()
            return True
        return False


class PSOPlanner(PathPlannerBase):
    """
    重构后的粒子群优化路径规划器
    
    主要改进：
    1. 更合理的粒子表示（关键路径点）
    2. 改进的适应度函数
    3. 更好的约束处理
    4. 统一的接口和数据结构
    """
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], 
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化PSO规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)
        
        # PSO特定参数
        self.max_iter = params.max_iterations if params else 100
        self.n_particles = 30
        self.n_waypoints = 8  # 路径关键点数量（包括起点和终点）
        self.w = 0.7          # 惯性权重
        self.c1 = 1.4         # 个体学习因子
        self.c2 = 1.4         # 社会学习因子
        self.w_min = 0.1      # 最小惯性权重
        self.w_max = 0.9      # 最大惯性权重
        
        # 从算法特定参数中获取配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.n_particles = params.algorithm_specific.get('n_particles', self.n_particles)
            self.n_waypoints = params.algorithm_specific.get('n_waypoints', self.n_waypoints)
            self.w = params.algorithm_specific.get('w', self.w)
            self.c1 = params.algorithm_specific.get('c1', self.c1)
            self.c2 = params.algorithm_specific.get('c2', self.c2)
            self.w_min = params.algorithm_specific.get('w_min', self.w_min)
            self.w_max = params.algorithm_specific.get('w_max', self.w_max)
        
        # 位置边界
        self.bounds = ((0, self.map_height - 1), (0, self.map_width - 1))
        
        # 粒子群
        self.particles = []
        self.global_best_position = None
        self.global_best_fitness = float('inf')
        
        # 统计信息
        self.fitness_history = []
        self.diversity_history = []
        self.convergence_history = []
        
        # 初始化粒子群
        self._initialize_particles()
        
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "PSO"
    
    def _initialize_particles(self):
        """初始化粒子群"""
        for _ in range(self.n_particles):
            # 生成随机路径关键点
            waypoints = self._generate_random_waypoints()
            
            # 创建粒子
            particle = PSOParticle(waypoints)
            
            # 评估初始适应度
            particle.current_fitness = self._evaluate_fitness(particle.position)
            particle.update_best()
            
            # 更新全局最优
            if particle.best_fitness < self.global_best_fitness:
                self.global_best_fitness = particle.best_fitness
                self.global_best_position = particle.best_position.copy()
            
            self.particles.append(particle)
    
    def _generate_random_waypoints(self) -> np.ndarray:
        """
        生成随机路径关键点
        
        Returns:
            np.ndarray: 路径关键点 shape: (n_waypoints, 2)
        """
        waypoints = np.zeros((self.n_waypoints, 2))
        
        # 设置起点和终点
        waypoints[0] = self.start
        waypoints[-1] = self.goal
        
        # 生成中间关键点
        for i in range(1, self.n_waypoints - 1):
            attempts = 0
            while attempts < 50:  # 限制尝试次数
                # 在地图范围内随机生成点
                row = random.uniform(0, self.map_height - 1)
                col = random.uniform(0, self.map_width - 1)
                
                # 检查是否在可通行区域
                if self._is_valid_continuous_point((row, col)):
                    waypoints[i] = [row, col]
                    break
                attempts += 1
            
            # 如果找不到有效点，使用线性插值
            if attempts >= 50:
                t = i / (self.n_waypoints - 1)
                waypoints[i] = (1 - t) * np.array(self.start) + t * np.array(self.goal)
        
        return waypoints
    
    def _is_valid_continuous_point(self, point: Tuple[float, float]) -> bool:
        """
        检查连续坐标点是否有效
        
        Args:
            point: 连续坐标点 (row, col)
            
        Returns:
            bool: 是否有效
        """
        row, col = point
        
        # 检查边界
        if row < 0 or row >= self.map_height or col < 0 or col >= self.map_width:
            return False
        
        # 检查是否在障碍物上（使用最近邻插值）
        grid_row, grid_col = int(round(row)), int(round(col))
        if (0 <= grid_row < self.map_height and 0 <= grid_col < self.map_width):
            return self.grid_map[grid_row, grid_col] == 0
        
        return False
    
    def plan(self) -> PlanningResult:
        """
        执行PSO路径规划
        
        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0
        
        try:
            for iteration in range(self.max_iter):
                self.iterations = iteration + 1
                
                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break
                
                # 动态调整惯性权重
                current_w = self.w_max - (self.w_max - self.w_min) * iteration / self.max_iter
                
                # 更新每个粒子
                for particle in self.particles:
                    # 更新速度
                    particle.update_velocity(self.global_best_position, current_w, self.c1, self.c2)
                    
                    # 更新位置
                    particle.update_position(self.bounds)
                    
                    # 评估适应度
                    particle.current_fitness = self._evaluate_fitness(particle.position)
                    
                    # 更新个体最优
                    particle.update_best()
                    
                    # 更新全局最优
                    if particle.best_fitness < self.global_best_fitness:
                        self.global_best_fitness = particle.best_fitness
                        self.global_best_position = particle.best_position.copy()
                
                # 记录统计信息
                self.fitness_history.append(self.global_best_fitness)
                self.diversity_history.append(self._calculate_diversity())
                self.convergence_history.append(self.global_best_fitness)
                
                # 打印进度
                if iteration % 20 == 0:
                    print(f"PSO迭代 {iteration}: 最佳适应度={self.global_best_fitness:.2f}")
            
            # 构建最终路径
            if self.global_best_position is not None:
                path = self._waypoints_to_path(self.global_best_position)
                
                computation_time = time.time() - self.start_time
                
                return PlanningResult(
                    path=path,
                    success=len(path) > 1,
                    cost=self.global_best_fitness,
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={
                        'fitness_history': self.fitness_history.copy(),
                        'diversity_history': self.diversity_history.copy(),
                        'convergence_history': self.convergence_history.copy(),
                        'n_particles': self.n_particles,
                        'n_waypoints': self.n_waypoints,
                        'final_waypoints': self.global_best_position.tolist()
                    }
                )
            else:
                computation_time = time.time() - self.start_time
                return PlanningResult(
                    path=None,
                    success=False,
                    cost=float('inf'),
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={'reason': 'No valid solution found'}
                )
                
        except Exception as e:
            computation_time = time.time() - self.start_time
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={'error': str(e)}
            )
    
    def _evaluate_fitness(self, waypoints: np.ndarray) -> float:
        """
        评估路径适应度
        
        Args:
            waypoints: 路径关键点
            
        Returns:
            float: 适应度值（越小越好）
        """
        fitness = 0.0
        
        # 1. 路径长度代价
        path_length = 0.0
        for i in range(len(waypoints) - 1):
            path_length += np.linalg.norm(waypoints[i + 1] - waypoints[i])
        fitness += path_length
        
        # 2. 碰撞惩罚
        collision_penalty = 0.0
        for i in range(len(waypoints) - 1):
            if self._check_segment_collision(waypoints[i], waypoints[i + 1]):
                collision_penalty += 1000.0
        fitness += collision_penalty
        
        # 3. 平滑度惩罚（减少急转弯）
        smoothness_penalty = 0.0
        if len(waypoints) >= 3:
            for i in range(1, len(waypoints) - 1):
                v1 = waypoints[i] - waypoints[i - 1]
                v2 = waypoints[i + 1] - waypoints[i]
                
                # 计算角度变化
                if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle_change = np.arccos(cos_angle)
                    smoothness_penalty += angle_change * 10  # 权重因子
        fitness += smoothness_penalty
        
        # 4. 距离目标的惩罚
        goal_distance = np.linalg.norm(waypoints[-1] - np.array(self.goal))
        if goal_distance > 1.0:
            fitness += goal_distance * 100
        
        return fitness
    
    def _check_segment_collision(self, start_point: np.ndarray, end_point: np.ndarray) -> bool:
        """
        检查路径段是否与障碍物碰撞
        
        Args:
            start_point: 起始点
            end_point: 结束点
            
        Returns:
            bool: True表示有碰撞
        """
        # 转换为整数坐标进行碰撞检测
        start_grid = (int(round(start_point[0])), int(round(start_point[1])))
        end_grid = (int(round(end_point[0])), int(round(end_point[1])))
        
        return PathPlanningUtils.check_collision_line(start_grid, end_grid, self.grid_map)
    
    def _calculate_diversity(self) -> float:
        """
        计算粒子群的多样性
        
        Returns:
            float: 多样性指标
        """
        if len(self.particles) < 2:
            return 0.0
        
        total_distance = 0.0
        count = 0
        
        for i in range(len(self.particles)):
            for j in range(i + 1, len(self.particles)):
                # 计算两个粒子之间的距离
                distance = np.linalg.norm(self.particles[i].position - self.particles[j].position)
                total_distance += distance
                count += 1
        
        return total_distance / count if count > 0 else 0.0
    
    def _waypoints_to_path(self, waypoints: np.ndarray) -> List[Tuple[int, int]]:
        """
        将关键点转换为完整路径
        
        Args:
            waypoints: 路径关键点
            
        Returns:
            List: 完整路径点列表
        """
        if len(waypoints) < 2:
            return []
        
        path = []
        
        for i in range(len(waypoints) - 1):
            start_point = waypoints[i]
            end_point = waypoints[i + 1]
            
            # 在两个关键点之间插值
            distance = np.linalg.norm(end_point - start_point)
            num_points = max(2, int(distance * 2))  # 根据距离确定插值点数
            
            for j in range(num_points):
                if i == len(waypoints) - 2 and j == num_points - 1:
                    # 最后一个点
                    t = 1.0
                else:
                    t = j / (num_points - 1) if num_points > 1 else 0
                
                interpolated_point = start_point * (1 - t) + end_point * t
                
                # 转换为整数网格坐标
                grid_point = (int(round(interpolated_point[0])), 
                             int(round(interpolated_point[1])))
                
                # 避免重复点
                if not path or path[-1] != grid_point:
                    path.append(grid_point)
        
        return path
    
    def visualize(self, result: PlanningResult, show_process: bool = False, 
                  save_path: Optional[str] = None):
        """
        可视化PSO结果
        
        Args:
            result: 规划结果
            show_process: 是否显示过程信息
            save_path: 保存路径
        """
        if show_process:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        else:
            fig, axes = plt.subplots(1, 1, figsize=(10, 8))
            axes = [axes]
        
        # 主路径图
        ax = axes[0] if show_process else axes
        
        # 显示地图
        ax.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制关键点和路径
        if result.success and result.path:
            path_array = np.array(result.path)
            ax.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=3, label='PSO Path')
            ax.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=4)
            
            # 绘制关键点
            if 'final_waypoints' in result.additional_data:
                waypoints = np.array(result.additional_data['final_waypoints'])
                ax.plot(waypoints[:, 1], waypoints[:, 0], 'ro', markersize=8, label='Waypoints')
        
        # 标记起点和终点
        ax.plot(self.start[1], self.start[0], 'go', markersize=15, label='Start')
        ax.plot(self.goal[1], self.goal[0], 'ro', markersize=15, label='Goal')
        
        # 设置标题
        status = "成功" if result.success else "失败"
        title = f'PSO算法 - {status}\n'
        title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s, 迭代: {result.iterations}'
        ax.set_title(title, fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        if show_process and 'fitness_history' in result.additional_data:
            # 适应度收敛曲线
            ax = axes[1]
            fitness_history = result.additional_data['fitness_history']
            ax.plot(fitness_history, 'b-', linewidth=2)
            ax.set_title('适应度收敛曲线')
            ax.set_xlabel('迭代次数')
            ax.set_ylabel('最佳适应度')
            ax.grid(True, alpha=0.3)
            
            # 多样性变化
            ax = axes[2]
            if 'diversity_history' in result.additional_data:
                diversity_history = result.additional_data['diversity_history']
                ax.plot(diversity_history, 'g-', linewidth=2)
                ax.set_title('粒子群多样性')
                ax.set_xlabel('迭代次数')
                ax.set_ylabel('多样性')
                ax.grid(True, alpha=0.3)
            
            # 统计信息
            ax = axes[3]
            ax.axis('off')
            stats_text = f"""
            PSO算法统计:
            
            粒子数量: {result.additional_data.get('n_particles', 'N/A')}
            关键点数量: {result.additional_data.get('n_waypoints', 'N/A')}
            迭代次数: {result.iterations}
            最佳适应度: {result.cost:.2f}
            
            参数设置:
            惯性权重: {self.w_min}-{self.w_max}
            个体学习因子: {self.c1}
            社会学习因子: {self.c2}
            """
            ax.text(0.1, 0.9, stats_text, transform=ax.transAxes, 
                   fontsize=12, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


def create_pso_params(max_iterations: int = 100, n_particles: int = 30,
                     n_waypoints: int = 8, w: float = 0.7, c1: float = 1.4, c2: float = 1.4,
                     w_min: float = 0.1, w_max: float = 0.9, timeout: float = 30.0) -> AlgorithmParams:
    """
    创建PSO算法参数
    
    Args:
        max_iterations: 最大迭代次数
        n_particles: 粒子数量
        n_waypoints: 路径关键点数量
        w: 惯性权重
        c1: 个体学习因子
        c2: 社会学习因子
        w_min: 最小惯性权重
        w_max: 最大惯性权重
        timeout: 超时时间
        
    Returns:
        AlgorithmParams: 算法参数
    """
    return AlgorithmParams(
        max_iterations=max_iterations,
        timeout=timeout,
        algorithm_specific={
            'max_iterations': max_iterations,
            'n_particles': n_particles,
            'n_waypoints': n_waypoints,
            'w': w,
            'c1': c1,
            'c2': c2,
            'w_min': w_min,
            'w_max': w_max
        }
    )


def test_pso_planner():
    """测试PSO规划器"""
    print("🧪 测试PSO路径规划器...")
    
    # 创建测试环境
    grid_map = np.zeros((20, 20))
    
    # 添加障碍物
    grid_map[8:12, 8] = 1    # 垂直墙
    grid_map[10, 5:12] = 1   # 水平墙
    grid_map[3:6, 3:6] = 1   # 小方块
    
    start = (2, 2)
    goal = (17, 17)
    
    print(f"测试环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 创建PSO参数
    params = create_pso_params(
        max_iterations=80,
        n_particles=20,
        n_waypoints=6,
        w=0.7,
        c1=1.4,
        c2=1.4,
        timeout=15.0
    )
    
    # 创建PSO规划器
    pso_planner = PSOPlanner(start, goal, grid_map, params)
    
    # 执行规划
    print("🚀 开始PSO规划...")
    result = pso_planner.plan()
    
    # 输出结果
    print(f"✅ PSO规划完成!")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   计算时间: {result.computation_time:.3f}秒")
    print(f"   迭代次数: {result.iterations}")
    
    # 可视化结果
    print("🎨 可视化结果...")
    pso_planner.visualize(result, show_process=True)
    
    # 测试路径指标
    if result.path:
        metrics = PathPlanningUtils.calculate_path_metrics(result.path)
        print(f"📊 路径指标:")
        print(f"   欧几里得长度: {metrics['euclidean_length']:.2f}")
        print(f"   平滑度: {metrics['smoothness']:.2f}")
        print(f"   转弯次数: {metrics['turn_count']}")
    
    return result


if __name__ == "__main__":
    test_pso_planner()