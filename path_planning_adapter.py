"""
路径规划算法适配器
统一接口，集成所有修复后的路径规划算法
"""

import numpy as np
import time
from typing import Dict, Any, List, Tuple, Optional

class PathPlanningResult:
    """路径规划结果类"""
    def __init__(self, path=None, success=False, cost=0.0, computation_time=0.0, 
                 iterations=0, algorithm_name="", additional_data=None):
        self.path = path
        self.success = success
        self.cost = cost
        self.computation_time = computation_time
        self.iterations = iterations
        self.algorithm_name = algorithm_name
        self.additional_data = additional_data or {}

class PathPlanningAdapter:
    """路径规划算法适配器"""
    
    @staticmethod
    def get_available_algorithms():
        """获取可用的算法列表"""
        return ['RRT', 'RRT*', 'ACO', 'PSO']
    
    @staticmethod
    def plan_path(algorithm: str, start: Tuple[int, int], goal: Tuple[int, int], 
                  grid_map: np.ndarray, params: Dict[str, Any] = None) -> PathPlanningResult:
        """
        统一的路径规划接口
        
        Args:
            algorithm: 算法名称 ('RRT', 'RRT*', 'ACO', 'PSO')
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图 (0=可通行, 1=障碍物)
            params: 算法参数字典
            
        Returns:
            PathPlanningResult: 规划结果
        """
        if params is None:
            params = {}
        
        try:
            if algorithm.upper() == 'RRT':
                return PathPlanningAdapter._run_rrt(start, goal, grid_map, params)
            elif algorithm.upper() == 'RRT*':
                return PathPlanningAdapter._run_rrt_star(start, goal, grid_map, params)
            elif algorithm.upper() == 'ACO':
                return PathPlanningAdapter._run_aco(start, goal, grid_map, params)
            elif algorithm.upper() == 'PSO':
                return PathPlanningAdapter._run_pso(start, goal, grid_map, params)
            else:
                return PathPlanningResult(
                    success=False,
                    algorithm_name=algorithm,
                    additional_data={'error': f'Unknown algorithm: {algorithm}'}
                )
        except Exception as e:
            return PathPlanningResult(
                success=False,
                algorithm_name=algorithm,
                additional_data={'error': str(e)}
            )
    
    @staticmethod
    def _run_rrt(start, goal, grid_map, params):
        """运行RRT算法"""
        try:
            from RRT import RRTPlanner, create_rrt_params
            
            # 创建参数
            rrt_params = create_rrt_params(
                max_iterations=params.get('max_iterations', 1500),
                step_size=params.get('step_size', 1.0),
                goal_bias=params.get('goal_bias', 0.1),
                timeout=params.get('timeout', 15.0)
            )
            
            # 创建规划器并执行
            planner = RRTPlanner(start, goal, grid_map, rrt_params)
            result = planner.plan()
            
            return result
            
        except ImportError:
            return PathPlanningResult(
                success=False,
                algorithm_name='RRT',
                additional_data={'error': 'RRT module not available'}
            )
    
    @staticmethod
    def _run_rrt_star(start, goal, grid_map, params):
        """运行RRT*算法"""
        try:
            from RRTstar import RRTStarPlanner, create_rrt_star_params
            
            # 创建参数
            rrt_star_params = create_rrt_star_params(
                max_iterations=params.get('max_iterations', 1500),
                step_size=params.get('step_size', 1.0),
                goal_bias=params.get('goal_bias', 0.1),
                initial_radius=params.get('initial_radius', 3.0),
                min_radius=params.get('min_radius', 1.0),
                timeout=params.get('timeout', 20.0)
            )
            
            # 创建规划器并执行
            planner = RRTStarPlanner(start, goal, grid_map, rrt_star_params)
            result = planner.plan()
            
            return result
            
        except ImportError:
            return PathPlanningResult(
                success=False,
                algorithm_name='RRT*',
                additional_data={'error': 'RRT* module not available'}
            )
    
    @staticmethod
    def _run_aco(start, goal, grid_map, params):
        """运行ACO算法"""
        try:
            from ACO_PathPlanning import ACOPlanner, create_aco_params
            
            # 创建参数
            aco_params = create_aco_params(
                max_iterations=params.get('max_iterations', 80),
                num_ants=params.get('num_ants', 15),
                alpha=params.get('alpha', 1.0),
                beta=params.get('beta', 2.0),
                evaporation_rate=params.get('evaporation_rate', 0.1),
                q0=params.get('q0', 0.9),
                timeout=params.get('timeout', 15.0)
            )
            
            # 创建规划器并执行
            planner = ACOPlanner(start, goal, grid_map, aco_params)
            result = planner.plan()
            
            return result
            
        except ImportError:
            return PathPlanningResult(
                success=False,
                algorithm_name='ACO',
                additional_data={'error': 'ACO module not available'}
            )
    
    @staticmethod
    def _run_pso(start, goal, grid_map, params):
        """运行PSO算法"""
        try:
            from PSO_PathPlanning import PSOPlanner, create_pso_params
            
            # 创建参数
            pso_params = create_pso_params(
                max_iterations=params.get('max_iterations', 100),
                num_particles=params.get('num_particles', 20),
                w=params.get('w', 0.7),
                c1=params.get('c1', 1.5),
                c2=params.get('c2', 1.5),
                path_length=params.get('path_length', 6),
                timeout=params.get('timeout', 15.0)
            )
            
            # 创建规划器并执行
            planner = PSOPlanner(start, goal, grid_map, pso_params)
            result = planner.plan()
            
            return result
            
        except ImportError:
            return PathPlanningResult(
                success=False,
                algorithm_name='PSO',
                additional_data={'error': 'PSO module not available'}
            )


# 为了向后兼容，保留旧的函数接口
def run_rrt_planning(start, goal, grid_map, return_dict=False, **kwargs):
    """RRT算法兼容接口"""
    result = PathPlanningAdapter.plan_path('RRT', start, goal, grid_map, kwargs)
    
    if return_dict:
        return {
            'path': result.path,
            'success': result.success,
            'cost': result.cost,
            'time': result.computation_time,
            'iterations': result.iterations,
            'exploration_tree': result.additional_data.get('exploration_edges', [])
        }
    else:
        return result.path

def run_rrt_star_planning(start, goal, grid_map, **kwargs):
    """RRT*算法兼容接口"""
    result = PathPlanningAdapter.plan_path('RRT*', start, goal, grid_map, kwargs)
    return result.path

def run_aco_planning(start, goal, grid_map, **kwargs):
    """ACO算法兼容接口"""
    result = PathPlanningAdapter.plan_path('ACO', start, goal, grid_map, kwargs)
    return result.path

def run_pso_planning(start, goal, grid_map, **kwargs):
    """PSO算法兼容接口"""
    result = PathPlanningAdapter.plan_path('PSO', start, goal, grid_map, kwargs)
    return result.path

def run_dqn_planning(start, goal, grid_map, **kwargs):
    """DQN算法兼容接口（暂未实现）"""
    return None


# 测试函数
def test_adapter():
    """测试适配器功能"""
    print("🧪 测试路径规划适配器...")
    
    # 创建测试环境
    grid_map = np.zeros((20, 20))
    grid_map[8:12, 8:12] = 1  # 添加障碍物
    
    start = (2, 2)
    goal = (17, 17)
    
    print(f"测试环境: {grid_map.shape}, 起点: {start}, 终点: {goal}")
    
    # 测试所有算法
    algorithms = PathPlanningAdapter.get_available_algorithms()
    print(f"可用算法: {algorithms}")
    
    for algorithm in algorithms:
        print(f"\n🔧 测试 {algorithm} 算法...")
        
        # 设置参数
        params = {
            'max_iterations': 500,
            'timeout': 10.0
        }
        
        # 执行规划
        result = PathPlanningAdapter.plan_path(algorithm, start, goal, grid_map, params)
        
        # 输出结果
        print(f"   成功: {result.success}")
        if result.success:
            print(f"   路径长度: {len(result.path)}")
            print(f"   代价: {result.cost:.2f}")
            print(f"   时间: {result.computation_time:.3f}s")
        else:
            print(f"   错误: {result.additional_data.get('error', 'Unknown error')}")
    
    print("\n✅ 适配器测试完成！")

if __name__ == "__main__":
    test_adapter()
