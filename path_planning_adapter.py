"""
路径规划适配器
用于将新的路径规划接口集成到海冰分割系统中
"""

import numpy as np
import time
from typing import Tuple, List, Optional, Dict, Any
from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils


class PathPlanningAdapter:
    """路径规划适配器类，提供与海冰分割系统兼容的接口"""
    
    @staticmethod
    def convert_grid_format(grid_map: np.ndarray) -> np.ndarray:
        """
        转换网格地图格式
        海冰分割系统可能使用不同的格式，这里进行标准化
        
        Args:
            grid_map: 输入的网格地图
            
        Returns:
            np.ndarray: 标准化的网格地图 (0=可通行, 1=障碍物)
        """
        # 确保是numpy数组
        grid_map = np.array(grid_map)
        
        # 如果是浮点数，转换为整数
        if grid_map.dtype in [np.float32, np.float64]:
            # 假设大于0.5的为障碍物
            grid_map = (grid_map > 0.5).astype(int)
        
        # 如果是布尔类型，转换为整数
        elif grid_map.dtype == bool:
            grid_map = grid_map.astype(int)
        
        # 确保只有0和1
        grid_map = np.clip(grid_map, 0, 1)
        
        return grid_map
    
    @staticmethod
    def validate_coordinates(start: Tuple[int, int], goal: Tuple[int, int], 
                           grid_map: np.ndarray) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        验证和修正坐标
        
        Args:
            start: 起点坐标
            goal: 终点坐标
            grid_map: 网格地图
            
        Returns:
            Tuple: (修正后的起点, 修正后的终点)
        """
        height, width = grid_map.shape
        
        # 修正起点
        start_row = max(0, min(height - 1, int(start[0])))
        start_col = max(0, min(width - 1, int(start[1])))
        corrected_start = (start_row, start_col)
        
        # 修正终点
        goal_row = max(0, min(height - 1, int(goal[0])))
        goal_col = max(0, min(width - 1, int(goal[1])))
        corrected_goal = (goal_row, goal_col)
        
        # 如果起点在障碍物上，寻找最近的可通行点
        if grid_map[corrected_start[0], corrected_start[1]] == 1:
            corrected_start = PathPlanningAdapter._find_nearest_free_point(
                corrected_start, grid_map)
        
        # 如果终点在障碍物上，寻找最近的可通行点
        if grid_map[corrected_goal[0], corrected_goal[1]] == 1:
            corrected_goal = PathPlanningAdapter._find_nearest_free_point(
                corrected_goal, grid_map)
        
        return corrected_start, corrected_goal
    
    @staticmethod
    def _find_nearest_free_point(point: Tuple[int, int], 
                                grid_map: np.ndarray) -> Tuple[int, int]:
        """
        寻找最近的可通行点
        
        Args:
            point: 目标点
            grid_map: 网格地图
            
        Returns:
            Tuple: 最近的可通行点
        """
        height, width = grid_map.shape
        row, col = point
        
        # 使用BFS搜索最近的可通行点
        from collections import deque
        queue = deque([(row, col, 0)])  # (row, col, distance)
        visited = set()
        
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                     (0, 1), (1, -1), (1, 0), (1, 1)]
        
        while queue:
            curr_row, curr_col, dist = queue.popleft()
            
            if (curr_row, curr_col) in visited:
                continue
            visited.add((curr_row, curr_col))
            
            # 检查边界
            if (0 <= curr_row < height and 0 <= curr_col < width and 
                grid_map[curr_row, curr_col] == 0):
                return (curr_row, curr_col)
            
            # 添加邻居
            for dr, dc in directions:
                new_row, new_col = curr_row + dr, curr_col + dc
                if (0 <= new_row < height and 0 <= new_col < width and 
                    (new_row, new_col) not in visited):
                    queue.append((new_row, new_col, dist + 1))
        
        # 如果找不到可通行点，返回原点
        return point
    
    @staticmethod
    def create_legacy_result(result: PlanningResult) -> Dict[str, Any]:
        """
        创建与旧系统兼容的结果格式
        
        Args:
            result: 新格式的规划结果
            
        Returns:
            Dict: 旧格式的结果
        """
        legacy_result = {
            'path': result.path,
            'success': result.success,
            'cost': result.cost,
            'time': result.computation_time,
            'iterations': result.iterations,
            'algorithm': result.algorithm_name
        }
        
        # 添加额外的指标
        if result.path:
            metrics = PathPlanningUtils.calculate_path_metrics(result.path)
            legacy_result.update({
                'path_length': metrics['length'],
                'euclidean_length': metrics['euclidean_length'],
                'smoothness': metrics['smoothness'],
                'turn_count': metrics['turn_count']
            })
        
        return legacy_result
    
    @staticmethod
    def run_algorithm_with_fallback(planner_class, start: Tuple[int, int], 
                                  goal: Tuple[int, int], grid_map: np.ndarray,
                                  params: Optional[AlgorithmParams] = None,
                                  max_retries: int = 3) -> PlanningResult:
        """
        运行算法并提供失败重试机制
        
        Args:
            planner_class: 规划器类
            start: 起点
            goal: 终点
            grid_map: 网格地图
            params: 算法参数
            max_retries: 最大重试次数
            
        Returns:
            PlanningResult: 规划结果
        """
        # 预处理输入
        grid_map = PathPlanningAdapter.convert_grid_format(grid_map)
        start, goal = PathPlanningAdapter.validate_coordinates(start, goal, grid_map)
        
        last_error = None
        
        for attempt in range(max_retries):
            try:
                # 创建规划器实例
                planner = planner_class(start, goal, grid_map, params)
                
                # 执行规划
                result = planner.plan()
                
                # 如果成功，返回结果
                if result.success:
                    return result
                
                # 如果失败但没有异常，调整参数重试
                if params and attempt < max_retries - 1:
                    # 增加迭代次数
                    params.max_iterations = int(params.max_iterations * 1.5)
                    # 减小步长
                    params.step_size = params.step_size * 0.8
                
            except Exception as e:
                last_error = e
                print(f"算法执行失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                # 调整参数重试
                if params and attempt < max_retries - 1:
                    params.max_iterations = min(params.max_iterations * 2, 5000)
                    params.step_size = max(params.step_size * 0.5, 0.1)
        
        # 所有尝试都失败，返回失败结果
        return PlanningResult(
            path=None,
            success=False,
            cost=float('inf'),
            computation_time=0.0,
            iterations=0,
            algorithm_name=planner_class.__name__,
            additional_data={'error': str(last_error) if last_error else 'Unknown error'}
        )


def create_adapter_functions():
    """创建适配器函数，用于替换海冰分割系统中的旧函数"""
    
    def run_rrt_planning(start, goal, grid_map, return_dict=False, **kwargs):
        """RRT算法适配器函数"""
        print("🚀 RRT算法适配器 - 使用新实现...")
        
        try:
            from rrt_planner import RRTPlanner, create_rrt_params
            
            # 创建RRT参数
            max_iter = kwargs.get('max_iterations', 1000)
            step_size = kwargs.get('step_size', 1.0)
            goal_bias = kwargs.get('goal_bias', 0.1)
            timeout = kwargs.get('timeout', 30.0)
            
            params = create_rrt_params(max_iter, step_size, goal_bias, timeout)
            
            # 运行RRT算法
            result = PathPlanningAdapter.run_algorithm_with_fallback(
                RRTPlanner, start, goal, grid_map, params, max_retries=2
            )
            
            if return_dict:
                exploration_edges = result.additional_data.get('exploration_edges', [])
                return {
                    'path': result.path,
                    'exploration_tree': exploration_edges,
                    'success': result.success,
                    'cost': result.cost,
                    'time': result.computation_time,
                    'iterations': result.iterations
                }
            else:
                return result.path
                
        except Exception as e:
            print(f"❌ RRT算法执行失败: {e}")
            # 返回失败结果
            if return_dict:
                return {
                    'path': None,
                    'exploration_tree': [],
                    'success': False,
                    'cost': float('inf'),
                    'time': 0.0,
                    'iterations': 0
                }
            else:
                return None
    
    def run_rrt_star_planning(start, goal, grid_map, **kwargs):
        """RRT*算法适配器函数"""
        print("🚀 RRT*算法适配器 - 使用新实现...")
        
        try:
            from rrt_star_planner import RRTStarPlanner, create_rrt_star_params
            
            # 创建RRT*参数
            max_iter = kwargs.get('max_iterations', 1000)
            step_size = kwargs.get('step_size', 1.0)
            goal_bias = kwargs.get('goal_bias', 0.1)
            timeout = kwargs.get('timeout', 30.0)
            initial_radius = kwargs.get('initial_radius', 2.0)
            min_radius = kwargs.get('min_radius', 0.5)
            
            params = create_rrt_star_params(
                max_iter, step_size, goal_bias, timeout, 
                initial_radius, 0.95, min_radius
            )
            
            # 运行RRT*算法
            result = PathPlanningAdapter.run_algorithm_with_fallback(
                RRTStarPlanner, start, goal, grid_map, params, max_retries=2
            )
            
            return result.path
                
        except Exception as e:
            print(f"❌ RRT*算法执行失败: {e}")
            return None
    
    def run_aco_planning(start, goal, grid_map, **kwargs):
        """ACO算法适配器函数"""
        print("🚀 ACO算法适配器 - 使用新实现...")
        
        try:
            from aco_planner import ACOPlanner, create_aco_params
            
            # 创建ACO参数
            max_iter = kwargs.get('max_iterations', 100)
            num_ants = kwargs.get('num_ants', 20)
            alpha = kwargs.get('alpha', 1.0)
            beta = kwargs.get('beta', 2.0)
            evaporation_rate = kwargs.get('evaporation_rate', 0.1)
            q0 = kwargs.get('q0', 0.9)
            timeout = kwargs.get('timeout', 30.0)
            
            params = create_aco_params(
                max_iter, num_ants, alpha, beta, 
                evaporation_rate, q0, timeout
            )
            
            # 运行ACO算法
            result = PathPlanningAdapter.run_algorithm_with_fallback(
                ACOPlanner, start, goal, grid_map, params, max_retries=2
            )
            
            return result.path
                
        except Exception as e:
            print(f"❌ ACO算法执行失败: {e}")
            return None
    
    def run_pso_planning(start, goal, grid_map, **kwargs):
        """PSO算法适配器函数"""
        print("🚀 PSO算法适配器 - 使用新实现...")
        
        try:
            from pso_planner import PSOPlanner, create_pso_params
            
            # 创建PSO参数
            max_iter = kwargs.get('max_iterations', 100)
            n_particles = kwargs.get('n_particles', 30)
            n_waypoints = kwargs.get('n_waypoints', 8)
            w = kwargs.get('w', 0.7)
            c1 = kwargs.get('c1', 1.4)
            c2 = kwargs.get('c2', 1.4)
            w_min = kwargs.get('w_min', 0.1)
            w_max = kwargs.get('w_max', 0.9)
            timeout = kwargs.get('timeout', 30.0)
            
            params = create_pso_params(
                max_iter, n_particles, n_waypoints, w, c1, c2,
                w_min, w_max, timeout
            )
            
            # 运行PSO算法
            result = PathPlanningAdapter.run_algorithm_with_fallback(
                PSOPlanner, start, goal, grid_map, params, max_retries=2
            )
            
            return result.path
                
        except Exception as e:
            print(f"❌ PSO算法执行失败: {e}")
            return None
    
    def run_dqn_planning(start, goal, grid_map, episodes=1000, **kwargs):
        """DQN算法适配器函数"""
        print("🔄 DQN算法适配器 - 保持原有实现...")
        
        # 这里保持原有的DQN实现，或者返回模拟结果
        result_path = [(start[0], start[1]), (goal[0], goal[1])]
        
        # 模拟DQN规划器对象
        class MockDQNPlanner:
            def visualize_training(self):
                print("DQN训练可视化（模拟）")
        
        return result_path, MockDQNPlanner()
    
    return {
        'run_rrt_planning': run_rrt_planning,
        'run_rrt_star_planning': run_rrt_star_planning,
        'run_aco_planning': run_aco_planning,
        'run_pso_planning': run_pso_planning,
        'run_dqn_planning': run_dqn_planning
    }


# 创建全局适配器函数
adapter_functions = create_adapter_functions()

# 导出适配器函数
run_rrt_planning = adapter_functions['run_rrt_planning']
run_rrt_star_planning = adapter_functions['run_rrt_star_planning']
run_aco_planning = adapter_functions['run_aco_planning']
run_pso_planning = adapter_functions['run_pso_planning']
run_dqn_planning = adapter_functions['run_dqn_planning']


def test_adapter():
    """测试适配器功能"""
    print("🧪 测试路径规划适配器...")
    
    # 创建测试环境
    grid_map = np.zeros((20, 20))
    grid_map[5:15, 8] = 1  # 添加障碍物
    
    start = (1, 1)
    goal = (18, 18)
    
    print(f"测试环境: {grid_map.shape}, 起点: {start}, 终点: {goal}")
    
    # 测试格式转换
    converted_map = PathPlanningAdapter.convert_grid_format(grid_map)
    print(f"✅ 地图格式转换: {converted_map.dtype}, 范围: [{converted_map.min()}, {converted_map.max()}]")
    
    # 测试坐标验证
    corrected_start, corrected_goal = PathPlanningAdapter.validate_coordinates(
        start, goal, converted_map)
    print(f"✅ 坐标验证: 起点 {start} -> {corrected_start}, 终点 {goal} -> {corrected_goal}")
    
    # 测试适配器函数
    print("🔄 测试适配器函数...")
    
    rrt_result = run_rrt_planning(start, goal, grid_map)
    print(f"✅ RRT适配器: 路径长度 {len(rrt_result) if rrt_result else 0}")
    
    aco_result = run_aco_planning(start, goal, grid_map)
    print(f"✅ ACO适配器: 路径长度 {len(aco_result) if aco_result else 0}")
    
    print("🎉 适配器测试完成！")


if __name__ == "__main__":
    test_adapter()