"""
测试修复后的PSO算法，确保不会穿越地形
"""

import numpy as np
import matplotlib.pyplot as plt
from PSO_PathPlanning import PSOPlanner, create_pso_params

def create_test_environment():
    """创建测试环境"""
    # 创建30x30的测试地图
    grid_map = np.zeros((30, 30))
    
    # 添加障碍物
    grid_map[10:20, 15] = 1  # 垂直墙
    grid_map[15, 5:25] = 1   # 水平墙
    grid_map[5:8, 5:8] = 1   # 小块障碍
    grid_map[22:25, 22:25] = 1  # 另一个小块障碍
    
    return grid_map

def test_pso_no_terrain_crossing():
    """测试PSO算法不穿越地形"""
    print("🧪 测试修复后的PSO算法...")
    
    # 创建测试环境
    grid_map = create_test_environment()
    start = (2, 2)
    goal = (27, 27)
    
    print(f"测试环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    print(f"障碍物数量: {np.sum(grid_map)}")
    
    # 创建PSO规划器
    params = create_pso_params(
        max_iterations=100,
        num_particles=20,
        w=0.7,
        c1=1.8,
        c2=1.8,
        path_length=10,
        timeout=15.0
    )
    
    planner = PSOPlanner(start, goal, grid_map, params)
    
    # 执行规划
    result = planner.plan()
    
    # 输出结果
    print(f"\n✅ PSO规划结果:")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   时间: {result.computation_time:.3f}s")
    print(f"   迭代次数: {result.iterations}")
    
    if result.success and result.path:
        print(f"   起点: {result.path[0]}")
        print(f"   终点: {result.path[-1]}")
        
        # 检查路径是否穿越障碍物
        path_valid = check_path_validity(result.path, grid_map)
        if path_valid:
            print("✅ 路径有效，没有穿越障碍物")
        else:
            print("❌ 路径无效，穿越了障碍物")
        
        # 可视化结果
        visualize_pso_result(result.path, grid_map, start, goal)
        
        return path_valid
    else:
        print("❌ PSO算法未找到路径")
        return False

def check_path_validity(path, grid_map):
    """检查路径是否有效（不穿越障碍物）"""
    if not path:
        return False
    
    # 检查每个路径点
    for point in path:
        row, col = int(round(point[0])), int(round(point[1]))
        if (0 <= row < grid_map.shape[0] and 0 <= col < grid_map.shape[1]):
            if grid_map[row, col] == 1:
                print(f"❌ 路径点 {point} 在障碍物中")
                return False
        else:
            print(f"❌ 路径点 {point} 超出边界")
            return False
    
    # 检查路径段
    for i in range(len(path) - 1):
        if check_line_collision(path[i], path[i + 1], grid_map):
            print(f"❌ 路径段 {path[i]} -> {path[i + 1]} 穿越障碍物")
            return False
    
    return True

def check_line_collision(start, end, grid_map):
    """检查线段是否与障碍物碰撞"""
    start_row, start_col = start
    end_row, end_col = end
    
    # 使用Bresenham算法检查线段
    distance = max(abs(end_row - start_row), abs(end_col - start_col))
    if distance == 0:
        return False
    
    for i in range(int(distance) + 1):
        t = i / distance if distance > 0 else 0
        check_row = int(start_row + t * (end_row - start_row))
        check_col = int(start_col + t * (end_col - start_col))
        
        if (0 <= check_row < grid_map.shape[0] and 0 <= check_col < grid_map.shape[1]):
            if grid_map[check_row, check_col] == 1:
                return True
        else:
            return True  # 超出边界也算碰撞
    
    return False

def visualize_pso_result(path, grid_map, start, goal):
    """可视化PSO结果"""
    plt.figure(figsize=(10, 8))
    
    # 显示地图
    plt.imshow(grid_map, cmap='gray_r', origin='upper', alpha=0.8)
    
    # 绘制路径
    if path:
        path_array = np.array(path)
        plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=3, label='PSO Path')
        plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=5)
        
        # 标记路径点编号
        for i, point in enumerate(path):
            if i % 2 == 0:  # 每隔一个点标记
                plt.text(point[1], point[0], str(i), fontsize=8, 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 标记起点和终点
    plt.plot(start[1], start[0], 'go', markersize=12, label='Start', markeredgecolor='black')
    plt.plot(goal[1], goal[0], 'ro', markersize=12, label='Goal', markeredgecolor='black')
    
    plt.title('修复后的PSO路径规划结果', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlabel('列 (Col)')
    plt.ylabel('行 (Row)')
    
    # 保存图片
    plt.savefig('pso_fixed_result.png', dpi=300, bbox_inches='tight')
    print("📊 结果已保存为 pso_fixed_result.png")
    plt.show()

def compare_before_after():
    """比较修复前后的效果"""
    print("\n" + "="*50)
    print("PSO算法修复效果对比")
    print("="*50)
    
    # 创建更复杂的测试环境
    grid_map = np.zeros((25, 25))
    
    # 创建一个需要绕行的障碍物配置
    grid_map[8:17, 12] = 1   # 垂直墙
    grid_map[12, 8:17] = 1   # 水平墙，形成L形障碍
    grid_map[5:8, 5:8] = 1   # 小块障碍
    grid_map[18:21, 18:21] = 1  # 另一个小块障碍
    
    start = (2, 2)
    goal = (22, 22)
    
    print(f"复杂测试环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    print(f"障碍物数量: {np.sum(grid_map)}")
    
    # 测试修复后的PSO
    params = create_pso_params(
        max_iterations=150,
        num_particles=25,
        w=0.6,
        c1=2.0,
        c2=2.0,
        path_length=12,
        timeout=20.0
    )
    
    planner = PSOPlanner(start, goal, grid_map, params)
    result = planner.plan()
    
    print(f"\n修复后的PSO结果:")
    print(f"   成功: {result.success}")
    if result.success:
        print(f"   路径长度: {len(result.path)}")
        print(f"   代价: {result.cost:.2f}")
        print(f"   时间: {result.computation_time:.3f}s")
        
        # 检查路径有效性
        path_valid = check_path_validity(result.path, grid_map)
        print(f"   路径有效性: {'✅ 有效' if path_valid else '❌ 无效'}")
        
        # 可视化复杂环境的结果
        plt.figure(figsize=(10, 8))
        plt.imshow(grid_map, cmap='gray_r', origin='upper', alpha=0.8)
        
        if result.path:
            path_array = np.array(result.path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=3, label='Fixed PSO Path')
            plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=4)
        
        plt.plot(start[1], start[0], 'go', markersize=12, label='Start')
        plt.plot(goal[1], goal[0], 'ro', markersize=12, label='Goal')
        plt.title('复杂环境中的PSO路径规划', fontsize=14)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('pso_complex_environment.png', dpi=300, bbox_inches='tight')
        print("📊 复杂环境结果已保存为 pso_complex_environment.png")
        plt.show()
        
        return path_valid
    else:
        print("   未找到路径")
        return False

def main():
    """主测试函数"""
    print("🚀 测试PSO算法地形穿越修复...")
    
    # 基本测试
    basic_test_passed = test_pso_no_terrain_crossing()
    
    # 复杂环境测试
    complex_test_passed = compare_before_after()
    
    # 总结
    print("\n" + "="*50)
    print("测试总结")
    print("="*50)
    
    if basic_test_passed:
        print("✅ 基本测试: 通过")
    else:
        print("❌ 基本测试: 失败")
    
    if complex_test_passed:
        print("✅ 复杂环境测试: 通过")
    else:
        print("❌ 复杂环境测试: 失败")
    
    if basic_test_passed and complex_test_passed:
        print("\n🎉 PSO算法地形穿越问题已修复！")
        print("算法现在能够正确避开障碍物，不会穿越地形。")
    else:
        print("\n⚠️ 部分测试失败，可能仍存在问题。")

if __name__ == "__main__":
    main()
