"""
修复后的RRT*路径规划算法
适用于海冰分割系统的网格地图环境
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any, Set
import matplotlib.pyplot as plt
from collections import deque

from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils


class RRTStarNode:
    """RRT*树节点"""
    def __init__(self, position: Tuple[float, float], parent: Optional['RRTStarNode'] = None):
        self.position = position  # (row, col) 位置
        self.parent = parent      # 父节点
        self.cost = 0.0          # 从起点到该节点的代价
        self.children: Set['RRTStarNode'] = set()  # 子节点集合
        
        if parent:
            self.cost = parent.cost + PathPlanningUtils.distance(parent.position, position)
            parent.children.add(self)
    
    def set_parent(self, new_parent: Optional['RRTStarNode']):
        """设置新的父节点"""
        # 从旧父节点的子节点中移除
        if self.parent:
            self.parent.children.discard(self)
        
        # 设置新父节点
        self.parent = new_parent
        if new_parent:
            new_parent.children.add(self)
            self.cost = new_parent.cost + PathPlanningUtils.distance(new_parent.position, self.position)
        else:
            self.cost = 0.0


class RRTStarPlanner(PathPlannerBase):
    """
    修复后的RRT*路径规划器
    
    主要改进：
    1. 动态搜索半径调整
    2. 高效的重连线机制
    3. 改进的成本更新策略
    4. 更好的内存管理
    """
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], 
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化RRT*规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)
        
        # RRT*特定参数
        self.max_iter = params.max_iterations if params else 1000
        self.step_size = params.step_size if params else 1.0
        self.goal_bias = params.goal_bias if params else 0.1
        
        # 从算法特定参数中获取额外配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.step_size = params.algorithm_specific.get('step_size', self.step_size)
            self.goal_bias = params.algorithm_specific.get('goal_bias', self.goal_bias)
            self.initial_radius = params.algorithm_specific.get('initial_radius', 2.0)
            self.radius_shrink_factor = params.algorithm_specific.get('radius_shrink_factor', 0.95)
            self.min_radius = params.algorithm_specific.get('min_radius', 0.5)
        else:
            self.initial_radius = 2.0
            self.radius_shrink_factor = 0.95
            self.min_radius = 0.5
        
        # 树结构
        self.nodes = []  # 存储所有节点
        self.start_node = RRTStarNode(start)
        self.nodes.append(self.start_node)
        
        # 动态搜索半径
        self.search_radius = self.initial_radius
        
        # 用于可视化的探索树边
        self.exploration_edges = []
        self.rewire_edges = []  # 记录重连线操作
        
        # 最佳路径跟踪
        self.best_goal_node = None
        self.best_cost = float('inf')
        
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "RRT*"
    
    def plan(self) -> PlanningResult:
        """
        执行RRT*路径规划
        
        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0
        
        try:
            for i in range(self.max_iter):
                self.iterations = i + 1
                
                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break
                
                # 动态调整搜索半径
                self._update_search_radius()
                
                # 随机采样
                if random.random() < self.goal_bias:
                    sample_point = self.goal
                else:
                    sample_point = self._random_sample()
                
                # 找到最近节点
                nearest_node = self._find_nearest_node(sample_point)
                
                # 扩展树
                new_node = self._extend_tree(nearest_node, sample_point)
                
                if new_node is None:
                    continue
                
                # 选择最优父节点
                self._choose_parent(new_node)
                
                # 添加到树中
                self.nodes.append(new_node)
                self.exploration_edges.append((new_node.parent.position, new_node.position))
                
                # 重连线优化
                self._rewire(new_node)
                
                # 检查是否到达目标
                if self._is_goal_reached(new_node):
                    goal_cost = new_node.cost + PathPlanningUtils.distance(new_node.position, self.goal)
                    if goal_cost < self.best_cost:
                        self.best_cost = goal_cost
                        self.best_goal_node = new_node
                
                # 如果找到了路径且已经优化了一段时间，可以提前结束
                if (self.best_goal_node is not None and 
                    i > 200 and  # 至少运行200次迭代
                    i % 100 == 0):  # 每100次迭代检查一次
                    # 如果最近100次迭代没有显著改进，可以结束
                    if hasattr(self, '_last_best_cost'):
                        improvement = (self._last_best_cost - self.best_cost) / self._last_best_cost
                        if improvement < 0.01:  # 改进小于1%
                            break
                    self._last_best_cost = self.best_cost
            
            # 构建结果
            computation_time = time.time() - self.start_time
            
            if self.best_goal_node is not None:
                # 找到路径
                path = self._extract_path(self.best_goal_node)
                
                return PlanningResult(
                    path=path,
                    success=True,
                    cost=self.best_cost,
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={
                        'tree_size': len(self.nodes),
                        'exploration_edges': self.exploration_edges.copy(),
                        'rewire_edges': self.rewire_edges.copy(),
                        'final_radius': self.search_radius,
                        'best_node_cost': self.best_goal_node.cost
                    }
                )
            else:
                # 未找到路径
                return PlanningResult(
                    path=None,
                    success=False,
                    cost=float('inf'),
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={
                        'tree_size': len(self.nodes),
                        'exploration_edges': self.exploration_edges.copy(),
                        'final_radius': self.search_radius,
                        'reason': 'No path found'
                    }
                )
                
        except Exception as e:
            computation_time = time.time() - self.start_time
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={
                    'error': str(e),
                    'tree_size': len(self.nodes)
                }
            )
    
    def _update_search_radius(self):
        """动态更新搜索半径"""
        # 基于节点数量的对数增长调整半径
        if len(self.nodes) > 1:
            # RRT*理论最优半径公式的简化版本
            gamma = 2.0 * (1 + 1/2) ** (1/2) * ((self.map_height * self.map_width) / math.pi) ** (1/2)
            optimal_radius = gamma * (math.log(len(self.nodes)) / len(self.nodes)) ** (1/2)
            
            # 限制半径范围
            self.search_radius = max(self.min_radius, min(self.initial_radius, optimal_radius))
    
    def _random_sample(self) -> Tuple[float, float]:
        """
        随机采样一个点
        
        Returns:
            Tuple: 随机点坐标 (row, col)
        """
        row = random.uniform(0, self.map_height - 1)
        col = random.uniform(0, self.map_width - 1)
        return (row, col)
    
    def _find_nearest_node(self, point: Tuple[float, float]) -> RRTStarNode:
        """
        找到树中距离给定点最近的节点
        
        Args:
            point: 目标点
            
        Returns:
            RRTStarNode: 最近的节点
        """
        min_distance = float('inf')
        nearest_node = self.start_node
        
        for node in self.nodes:
            distance = PathPlanningUtils.distance(node.position, point)
            if distance < min_distance:
                min_distance = distance
                nearest_node = node
        
        return nearest_node
    
    def _extend_tree(self, nearest_node: RRTStarNode, 
                    sample_point: Tuple[float, float]) -> Optional[RRTStarNode]:
        """
        从最近节点向采样点扩展树
        
        Args:
            nearest_node: 最近节点
            sample_point: 采样点
            
        Returns:
            Optional[RRTStarNode]: 新节点，如果扩展失败则返回None
        """
        # 计算方向和距离
        direction = np.array(sample_point) - np.array(nearest_node.position)
        distance = np.linalg.norm(direction)
        
        if distance == 0:
            return None
        
        # 限制步长
        if distance > self.step_size:
            direction = direction / distance * self.step_size
        
        # 计算新位置
        new_position = tuple(np.array(nearest_node.position) + direction)
        
        # 检查新位置是否有效
        if not self._is_valid_position(new_position):
            return None
        
        # 检查路径是否无碰撞
        if self._check_collision_path(nearest_node.position, new_position):
            return None
        
        # 创建新节点（暂时以nearest_node为父节点）
        new_node = RRTStarNode(new_position, nearest_node)
        return new_node
    
    def _choose_parent(self, new_node: RRTStarNode):
        """
        为新节点选择最优父节点
        
        Args:
            new_node: 新节点
        """
        # 获取搜索半径内的所有节点
        nearby_nodes = self._get_nodes_in_radius(new_node.position, self.search_radius)
        
        min_cost = new_node.cost
        best_parent = new_node.parent
        
        for node in nearby_nodes:
            if node == new_node:
                continue
            
            # 计算通过该节点的潜在代价
            potential_cost = node.cost + PathPlanningUtils.distance(node.position, new_node.position)
            
            # 如果代价更低且路径无碰撞
            if (potential_cost < min_cost and 
                not self._check_collision_path(node.position, new_node.position)):
                min_cost = potential_cost
                best_parent = node
        
        # 更新父节点
        if best_parent != new_node.parent:
            new_node.set_parent(best_parent)
    
    def _rewire(self, new_node: RRTStarNode):
        """
        重连线优化：检查是否可以通过新节点改进附近节点的路径
        
        Args:
            new_node: 新添加的节点
        """
        # 获取搜索半径内的所有节点
        nearby_nodes = self._get_nodes_in_radius(new_node.position, self.search_radius)
        
        for node in nearby_nodes:
            if node == new_node or node == new_node.parent:
                continue
            
            # 计算通过新节点的潜在代价
            potential_cost = new_node.cost + PathPlanningUtils.distance(new_node.position, node.position)
            
            # 如果代价更低且路径无碰撞
            if (potential_cost < node.cost and 
                not self._check_collision_path(new_node.position, node.position)):
                
                # 记录重连线操作
                old_parent = node.parent
                if old_parent:
                    self.rewire_edges.append((old_parent.position, node.position, 'removed'))
                self.rewire_edges.append((new_node.position, node.position, 'added'))
                
                # 更新父节点
                node.set_parent(new_node)
                
                # 更新所有子树的代价
                self._update_subtree_cost(node)
    
    def _get_nodes_in_radius(self, center: Tuple[float, float], 
                           radius: float) -> List[RRTStarNode]:
        """
        获取指定半径内的所有节点
        
        Args:
            center: 中心点
            radius: 搜索半径
            
        Returns:
            List[RRTStarNode]: 半径内的节点列表
        """
        nearby_nodes = []
        for node in self.nodes:
            if PathPlanningUtils.distance(node.position, center) <= radius:
                nearby_nodes.append(node)
        return nearby_nodes
    
    def _update_subtree_cost(self, root_node: RRTStarNode):
        """
        更新子树的代价（使用BFS避免递归深度问题）
        
        Args:
            root_node: 子树根节点
        """
        queue = deque([root_node])
        
        while queue:
            current_node = queue.popleft()
            
            # 更新所有子节点
            for child in current_node.children:
                old_cost = child.cost
                new_cost = current_node.cost + PathPlanningUtils.distance(
                    current_node.position, child.position)
                
                if abs(new_cost - old_cost) > 1e-6:  # 只有代价真正改变时才更新
                    child.cost = new_cost
                    queue.append(child)
    
    def _is_valid_position(self, position: Tuple[float, float]) -> bool:
        """
        检查位置是否有效
        
        Args:
            position: 位置坐标
            
        Returns:
            bool: 是否有效
        """
        row, col = position
        
        # 检查边界
        if row < 0 or row >= self.map_height or col < 0 or col >= self.map_width:
            return False
        
        # 检查是否在障碍物上
        grid_row, grid_col = int(row), int(col)
        if (0 <= grid_row < self.map_height and 0 <= grid_col < self.map_width):
            return self.grid_map[grid_row, grid_col] == 0
        
        return False
    
    def _check_collision_path(self, start_pos: Tuple[float, float], 
                            end_pos: Tuple[float, float]) -> bool:
        """
        检查路径是否与障碍物碰撞
        
        Args:
            start_pos: 起始位置
            end_pos: 结束位置
            
        Returns:
            bool: True表示有碰撞
        """
        return PathPlanningUtils.check_collision_line(
            (int(start_pos[0]), int(start_pos[1])),
            (int(end_pos[0]), int(end_pos[1])),
            self.grid_map,
            step_size=0.5
        )
    
    def _is_goal_reached(self, node: RRTStarNode) -> bool:
        """
        检查是否到达目标
        
        Args:
            node: 当前节点
            
        Returns:
            bool: 是否到达目标
        """
        distance = PathPlanningUtils.distance(node.position, self.goal)
        
        # 如果距离足够近且路径无碰撞
        if distance <= self.step_size:
            return not self._check_collision_path(node.position, self.goal)
        
        return False
    
    def _extract_path(self, goal_node: RRTStarNode) -> List[Tuple[int, int]]:
        """
        从目标节点提取路径
        
        Args:
            goal_node: 目标节点
            
        Returns:
            List: 路径点列表
        """
        path = []
        current_node = goal_node
        
        # 从目标节点回溯到起点
        while current_node is not None:
            # 转换为整数坐标
            grid_pos = (int(round(current_node.position[0])), 
                       int(round(current_node.position[1])))
            path.append(grid_pos)
            current_node = current_node.parent
        
        # 确保路径以目标点结束
        goal_grid = (int(round(self.goal[0])), int(round(self.goal[1])))
        if path and path[0] != goal_grid:
            path.insert(0, goal_grid)
        
        # 反转路径（从起点到终点）
        path.reverse()
        
        return path
    
    def visualize(self, result: PlanningResult, show_process: bool = False, 
                  save_path: Optional[str] = None):
        """
        可视化RRT*结果
        
        Args:
            result: 规划结果
            show_process: 是否显示探索过程
            save_path: 保存路径
        """
        plt.figure(figsize=(12, 10))
        
        # 显示地图
        plt.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制探索树
        if show_process and 'exploration_edges' in result.additional_data:
            edges = result.additional_data['exploration_edges']
            for parent_pos, child_pos in edges:
                plt.plot([parent_pos[1], child_pos[1]], 
                        [parent_pos[0], child_pos[0]], 
                        'cyan', alpha=0.3, linewidth=0.5)
        
        # 绘制重连线操作
        if show_process and 'rewire_edges' in result.additional_data:
            rewire_edges = result.additional_data['rewire_edges']
            for edge_info in rewire_edges:
                if len(edge_info) == 3:
                    pos1, pos2, action = edge_info
                    if action == 'added':
                        plt.plot([pos1[1], pos2[1]], [pos1[0], pos2[0]], 
                                'orange', alpha=0.6, linewidth=1.5)
        
        # 绘制路径
        if result.success and result.path:
            path_array = np.array(result.path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=4, label='RRT* Path')
            plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=5)
        
        # 标记起点和终点
        plt.plot(self.start[1], self.start[0], 'go', markersize=15, label='Start')
        plt.plot(self.goal[1], self.goal[0], 'ro', markersize=15, label='Goal')
        
        # 设置标题
        status = "成功" if result.success else "失败"
        title = f'RRT*算法 - {status}\n'
        title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s, 迭代: {result.iterations}'
        if 'tree_size' in result.additional_data:
            title += f', 树大小: {result.additional_data["tree_size"]}'
        if 'final_radius' in result.additional_data:
            title += f', 最终半径: {result.additional_data["final_radius"]:.2f}'
        
        plt.title(title, fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


def create_rrt_star_params(max_iterations: int = 1000, step_size: float = 1.0, 
                          goal_bias: float = 0.1, timeout: float = 30.0,
                          initial_radius: float = 2.0, radius_shrink_factor: float = 0.95,
                          min_radius: float = 0.5) -> AlgorithmParams:
    """
    创建RRT*算法参数
    
    Args:
        max_iterations: 最大迭代次数
        step_size: 步长
        goal_bias: 目标偏置概率
        timeout: 超时时间
        initial_radius: 初始搜索半径
        radius_shrink_factor: 半径收缩因子
        min_radius: 最小搜索半径
        
    Returns:
        AlgorithmParams: 算法参数
    """
    return AlgorithmParams(
        max_iterations=max_iterations,
        step_size=step_size,
        goal_bias=goal_bias,
        timeout=timeout,
        algorithm_specific={
            'max_iterations': max_iterations,
            'step_size': step_size,
            'goal_bias': goal_bias,
            'initial_radius': initial_radius,
            'radius_shrink_factor': radius_shrink_factor,
            'min_radius': min_radius
        }
    )


def test_rrt_star_planner():
    """测试RRT*规划器"""
    print("🧪 测试RRT*路径规划器...")
    
    # 创建测试环境
    grid_map = np.zeros((30, 30))
    
    # 添加障碍物
    grid_map[10:20, 10] = 1    # 垂直墙
    grid_map[15, 5:15] = 1     # 水平墙
    grid_map[5:8, 5:8] = 1     # 小方块
    grid_map[22:25, 22:25] = 1 # 另一个小方块
    
    start = (2, 2)
    goal = (27, 27)
    
    print(f"测试环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 创建RRT*参数
    params = create_rrt_star_params(
        max_iterations=2000,
        step_size=1.5,
        goal_bias=0.15,
        timeout=15.0,
        initial_radius=3.0,
        min_radius=1.0
    )
    
    # 创建RRT*规划器
    rrt_star_planner = RRTStarPlanner(start, goal, grid_map, params)
    
    # 执行规划
    print("🚀 开始RRT*规划...")
    result = rrt_star_planner.plan()
    
    # 输出结果
    print(f"✅ RRT*规划完成!")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   计算时间: {result.computation_time:.3f}秒")
    print(f"   迭代次数: {result.iterations}")
    
    if 'tree_size' in result.additional_data:
        print(f"   探索树大小: {result.additional_data['tree_size']}")
    if 'final_radius' in result.additional_data:
        print(f"   最终搜索半径: {result.additional_data['final_radius']:.2f}")
    if 'rewire_edges' in result.additional_data:
        print(f"   重连线操作: {len(result.additional_data['rewire_edges'])}次")
    
    # 可视化结果
    print("🎨 可视化结果...")
    rrt_star_planner.visualize(result, show_process=True)
    
    # 测试路径指标
    if result.path:
        metrics = PathPlanningUtils.calculate_path_metrics(result.path)
        print(f"📊 路径指标:")
        print(f"   欧几里得长度: {metrics['euclidean_length']:.2f}")
        print(f"   平滑度: {metrics['smoothness']:.2f}")
        print(f"   转弯次数: {metrics['turn_count']}")
    
    return result


if __name__ == "__main__":
    test_rrt_star_planner()