"""
重构后的蚁群优化(ACO)路径规划算法
适用于海冰分割系统的网格地图环境
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt
from collections import defaultdict

from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils


class ACOAnt:
    """蚂蚁类"""
    def __init__(self, start_pos: Tuple[int, int]):
        self.current_pos = start_pos
        self.path = [start_pos]
        self.visited = {start_pos}
        self.path_cost = 0.0
        self.is_stuck = False
        
    def move_to(self, new_pos: Tuple[int, int]):
        """移动到新位置"""
        if new_pos not in self.visited:
            self.path_cost += PathPlanningUtils.distance(self.current_pos, new_pos)
            self.current_pos = new_pos
            self.path.append(new_pos)
            self.visited.add(new_pos)
            return True
        return False
    
    def reset(self, start_pos: Tuple[int, int]):
        """重置蚂蚁状态"""
        self.current_pos = start_pos
        self.path = [start_pos]
        self.visited = {start_pos}
        self.path_cost = 0.0
        self.is_stuck = False


class ACOPlanner(PathPlannerBase):
    """
    重构后的蚁群优化路径规划器
    
    主要改进：
    1. 基于网格的信息素矩阵
    2. 改进的启发式函数
    3. 更好的路径构建策略
    4. 统一的接口和数据结构
    """
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], 
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化ACO规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)
        
        # ACO特定参数
        self.max_iter = params.max_iterations if params else 100
        self.num_ants = 20
        self.alpha = 1.0  # 信息素重要性
        self.beta = 2.0   # 启发式重要性
        self.evaporation_rate = 0.1
        self.q0 = 0.9     # 贪婪选择概率
        self.initial_pheromone = 1.0
        
        # 从算法特定参数中获取配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.num_ants = params.algorithm_specific.get('num_ants', self.num_ants)
            self.alpha = params.algorithm_specific.get('alpha', self.alpha)
            self.beta = params.algorithm_specific.get('beta', self.beta)
            self.evaporation_rate = params.algorithm_specific.get('evaporation_rate', self.evaporation_rate)
            self.q0 = params.algorithm_specific.get('q0', self.q0)
            self.initial_pheromone = params.algorithm_specific.get('initial_pheromone', self.initial_pheromone)
        
        # 信息素矩阵 - 基于网格位置
        self.pheromone = np.full((self.map_height, self.map_width), self.initial_pheromone)
        
        # 启发式信息矩阵 - 预计算距离目标的倒数
        self.heuristic = self._compute_heuristic_matrix()
        
        # 蚂蚁群体
        self.ants = [ACOAnt(start) for _ in range(self.num_ants)]
        
        # 最佳解跟踪
        self.best_path = None
        self.best_cost = float('inf')
        self.convergence_history = []
        self.iteration_best_costs = []
        
        # 统计信息
        self.successful_ants_per_iteration = []
        self.average_path_length_per_iteration = []
        
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "ACO"
    
    def _compute_heuristic_matrix(self) -> np.ndarray:
        """
        预计算启发式信息矩阵
        
        Returns:
            np.ndarray: 启发式信息矩阵
        """
        heuristic = np.zeros((self.map_height, self.map_width))
        
        for i in range(self.map_height):
            for j in range(self.map_width):
                if self.grid_map[i, j] == 0:  # 只计算可通行区域
                    distance = PathPlanningUtils.distance((i, j), self.goal)
                    heuristic[i, j] = 1.0 / (distance + 1e-6)
                else:
                    heuristic[i, j] = 0.0  # 障碍物区域启发式为0
        
        return heuristic
    
    def plan(self) -> PlanningResult:
        """
        执行ACO路径规划
        
        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0
        
        try:
            for iteration in range(self.max_iter):
                self.iterations = iteration + 1
                
                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break
                
                # 重置所有蚂蚁
                for ant in self.ants:
                    ant.reset(self.start)
                
                # 构建解
                iteration_paths = []
                successful_ants = 0
                
                for ant in self.ants:
                    path = self._construct_solution(ant)
                    if path and len(path) > 1:
                        iteration_paths.append(path)
                        
                        # 检查是否到达目标
                        if PathPlanningUtils.distance(path[-1], self.goal) <= 1.5:
                            successful_ants += 1
                            
                            # 更新全局最优
                            path_cost = self._calculate_path_cost(path)
                            if path_cost < self.best_cost:
                                self.best_cost = path_cost
                                self.best_path = path.copy()
                
                # 更新信息素
                self._update_pheromone(iteration_paths)
                
                # 记录统计信息
                self.successful_ants_per_iteration.append(successful_ants)
                if iteration_paths:
                    avg_length = np.mean([len(path) for path in iteration_paths])
                    self.average_path_length_per_iteration.append(avg_length)
                else:
                    self.average_path_length_per_iteration.append(0)
                
                self.convergence_history.append(self.best_cost)
                
                # 打印进度
                if iteration % 20 == 0:
                    print(f"ACO迭代 {iteration}: 最佳代价={self.best_cost:.2f}, 成功蚂蚁={successful_ants}")
            
            # 构建结果
            computation_time = time.time() - self.start_time
            
            if self.best_path is not None:
                return PlanningResult(
                    path=self.best_path,
                    success=True,
                    cost=self.best_cost,
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={
                        'convergence_history': self.convergence_history.copy(),
                        'successful_ants_per_iteration': self.successful_ants_per_iteration.copy(),
                        'average_path_length_per_iteration': self.average_path_length_per_iteration.copy(),
                        'final_pheromone_max': float(np.max(self.pheromone)),
                        'final_pheromone_min': float(np.min(self.pheromone))
                    }
                )
            else:
                return PlanningResult(
                    path=None,
                    success=False,
                    cost=float('inf'),
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={
                        'convergence_history': self.convergence_history.copy(),
                        'reason': 'No valid path found'
                    }
                )
                
        except Exception as e:
            computation_time = time.time() - self.start_time
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={
                    'error': str(e)
                }
            )
    
    def _construct_solution(self, ant: ACOAnt) -> Optional[List[Tuple[int, int]]]:
        """
        为单只蚂蚁构建解
        
        Args:
            ant: 蚂蚁对象
            
        Returns:
            Optional[List]: 路径，如果构建失败返回None
        """
        max_steps = self.map_height * self.map_width  # 防止无限循环
        
        for step in range(max_steps):
            # 检查是否到达目标
            if PathPlanningUtils.distance(ant.current_pos, self.goal) <= 1.5:
                # 尝试直接连接到目标
                if not PathPlanningUtils.check_collision_line(ant.current_pos, self.goal, self.grid_map):
                    ant.move_to(self.goal)
                break
            
            # 获取可行的下一步位置
            candidates = self._get_feasible_neighbors(ant)
            
            if not candidates:
                ant.is_stuck = True
                break
            
            # 选择下一步
            next_pos = self._select_next_position(ant, candidates)
            
            if next_pos is None or not ant.move_to(next_pos):
                ant.is_stuck = True
                break
        
        return ant.path if len(ant.path) > 1 else None
    
    def _get_feasible_neighbors(self, ant: ACOAnt) -> List[Tuple[int, int]]:
        """
        获取蚂蚁的可行邻居位置
        
        Args:
            ant: 蚂蚁对象
            
        Returns:
            List: 可行邻居位置列表
        """
        neighbors = PathPlanningUtils.get_neighbors(ant.current_pos, self.grid_map, include_diagonal=True)
        
        # 过滤掉已访问的位置
        feasible = []
        for neighbor in neighbors:
            if neighbor not in ant.visited:
                feasible.append(neighbor)
        
        # 如果没有未访问的邻居，允许重访但增加惩罚
        if not feasible and len(neighbors) > 0:
            feasible = neighbors
        
        return feasible
    
    def _select_next_position(self, ant: ACOAnt, candidates: List[Tuple[int, int]]) -> Optional[Tuple[int, int]]:
        """
        基于ACO规则选择下一个位置
        
        Args:
            ant: 蚂蚁对象
            candidates: 候选位置列表
            
        Returns:
            Optional[Tuple]: 选择的位置，如果无法选择返回None
        """
        if not candidates:
            return None
        
        # 计算每个候选位置的吸引力
        attractions = []
        for pos in candidates:
            row, col = pos
            
            # 信息素强度
            pheromone_strength = self.pheromone[row, col]
            
            # 启发式信息
            heuristic_value = self.heuristic[row, col]
            
            # 访问惩罚
            visit_penalty = 1.0
            if pos in ant.visited:
                visit_penalty = 0.1  # 重访惩罚
            
            # 计算吸引力
            attraction = (pheromone_strength ** self.alpha) * (heuristic_value ** self.beta) * visit_penalty
            attractions.append(attraction)
        
        attractions = np.array(attractions)
        
        # 避免所有吸引力为0的情况
        if np.sum(attractions) == 0:
            return random.choice(candidates)
        
        # ACO选择策略
        if random.random() < self.q0:
            # 贪婪选择
            best_idx = np.argmax(attractions)
            return candidates[best_idx]
        else:
            # 概率选择
            probabilities = attractions / np.sum(attractions)
            selected_idx = np.random.choice(len(candidates), p=probabilities)
            return candidates[selected_idx]
    
    def _calculate_path_cost(self, path: List[Tuple[int, int]]) -> float:
        """
        计算路径代价
        
        Args:
            path: 路径
            
        Returns:
            float: 路径代价
        """
        if len(path) < 2:
            return float('inf')
        
        cost = 0.0
        for i in range(len(path) - 1):
            cost += PathPlanningUtils.distance(path[i], path[i + 1])
        
        # 如果没有到达目标，增加惩罚
        if PathPlanningUtils.distance(path[-1], self.goal) > 1.5:
            cost += PathPlanningUtils.distance(path[-1], self.goal) * 10
        
        return cost
    
    def _update_pheromone(self, paths: List[List[Tuple[int, int]]]):
        """
        更新信息素
        
        Args:
            paths: 本次迭代所有蚂蚁的路径
        """
        # 信息素蒸发
        self.pheromone *= (1 - self.evaporation_rate)
        
        # 信息素沉积
        for path in paths:
            if len(path) < 2:
                continue
            
            path_cost = self._calculate_path_cost(path)
            if path_cost == float('inf'):
                continue
            
            # 计算信息素增量
            delta_pheromone = 1.0 / path_cost
            
            # 如果是最佳路径，给予额外奖励
            if path == self.best_path:
                delta_pheromone *= 2.0
            
            # 在路径上沉积信息素
            for i in range(len(path) - 1):
                pos1, pos2 = path[i], path[i + 1]
                
                # 在两个位置之间的所有点上沉积信息素
                self._deposit_pheromone_on_segment(pos1, pos2, delta_pheromone)
        
        # 限制信息素范围，避免过度积累
        self.pheromone = np.clip(self.pheromone, 0.01, 100.0)
    
    def _deposit_pheromone_on_segment(self, pos1: Tuple[int, int], pos2: Tuple[int, int], delta: float):
        """
        在路径段上沉积信息素
        
        Args:
            pos1: 起始位置
            pos2: 结束位置
            delta: 信息素增量
        """
        # 在两个位置上直接沉积
        self.pheromone[pos1[0], pos1[1]] += delta
        self.pheromone[pos2[0], pos2[1]] += delta
        
        # 如果两点不相邻，在中间路径上也沉积
        if PathPlanningUtils.distance(pos1, pos2) > 1.5:
            # 使用Bresenham算法或简单插值
            steps = max(abs(pos2[0] - pos1[0]), abs(pos2[1] - pos1[1]))
            for i in range(1, steps):
                t = i / steps
                inter_row = int(pos1[0] + t * (pos2[0] - pos1[0]))
                inter_col = int(pos1[1] + t * (pos2[1] - pos1[1]))
                if (0 <= inter_row < self.map_height and 0 <= inter_col < self.map_width):
                    self.pheromone[inter_row, inter_col] += delta * 0.5
    
    def visualize(self, result: PlanningResult, show_process: bool = False, 
                  save_path: Optional[str] = None):
        """
        可视化ACO结果
        
        Args:
            result: 规划结果
            show_process: 是否显示过程信息
            save_path: 保存路径
        """
        if show_process:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        else:
            fig, axes = plt.subplots(1, 1, figsize=(10, 8))
            axes = [axes]
        
        # 主路径图
        ax = axes[0] if show_process else axes
        
        # 显示地图
        ax.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 显示信息素分布
        if show_process:
            pheromone_display = np.ma.masked_where(self.grid_map == 1, self.pheromone)
            im = ax.imshow(pheromone_display, cmap='hot', origin='upper', alpha=0.3)
            plt.colorbar(im, ax=ax, label='信息素浓度')
        
        # 绘制路径
        if result.success and result.path:
            path_array = np.array(result.path)
            ax.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=4, label='ACO Path')
            ax.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=5)
        
        # 标记起点和终点
        ax.plot(self.start[1], self.start[0], 'go', markersize=15, label='Start')
        ax.plot(self.goal[1], self.goal[0], 'ro', markersize=15, label='Goal')
        
        # 设置标题
        status = "成功" if result.success else "失败"
        title = f'ACO算法 - {status}\n'
        title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s, 迭代: {result.iterations}'
        ax.set_title(title, fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        if show_process and 'convergence_history' in result.additional_data:
            # 收敛曲线
            ax = axes[1]
            convergence = result.additional_data['convergence_history']
            ax.plot(convergence, 'b-', linewidth=2)
            ax.set_title('收敛曲线')
            ax.set_xlabel('迭代次数')
            ax.set_ylabel('最佳代价')
            ax.grid(True, alpha=0.3)
            
            # 成功蚂蚁数量
            ax = axes[2]
            if 'successful_ants_per_iteration' in result.additional_data:
                successful_ants = result.additional_data['successful_ants_per_iteration']
                ax.plot(successful_ants, 'g-', linewidth=2)
                ax.set_title('每次迭代成功蚂蚁数量')
                ax.set_xlabel('迭代次数')
                ax.set_ylabel('成功蚂蚁数量')
                ax.grid(True, alpha=0.3)
            
            # 统计信息
            ax = axes[3]
            ax.axis('off')
            stats_text = f"""
            ACO算法统计:
            
            蚂蚁数量: {self.num_ants}
            迭代次数: {result.iterations}
            最佳代价: {result.cost:.2f}
            
            参数设置:
            α (信息素): {self.alpha}
            β (启发式): {self.beta}
            蒸发率: {self.evaporation_rate}
            贪婪概率: {self.q0}
            """
            ax.text(0.1, 0.9, stats_text, transform=ax.transAxes, 
                   fontsize=12, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


def create_aco_params(max_iterations: int = 100, num_ants: int = 20,
                     alpha: float = 1.0, beta: float = 2.0,
                     evaporation_rate: float = 0.1, q0: float = 0.9,
                     timeout: float = 30.0) -> AlgorithmParams:
    """
    创建ACO算法参数
    
    Args:
        max_iterations: 最大迭代次数
        num_ants: 蚂蚁数量
        alpha: 信息素重要性因子
        beta: 启发式重要性因子
        evaporation_rate: 信息素蒸发率
        q0: 贪婪选择概率
        timeout: 超时时间
        
    Returns:
        AlgorithmParams: 算法参数
    """
    return AlgorithmParams(
        max_iterations=max_iterations,
        timeout=timeout,
        algorithm_specific={
            'max_iterations': max_iterations,
            'num_ants': num_ants,
            'alpha': alpha,
            'beta': beta,
            'evaporation_rate': evaporation_rate,
            'q0': q0,
            'initial_pheromone': 1.0
        }
    )


def test_aco_planner():
    """测试ACO规划器"""
    print("🧪 测试ACO路径规划器...")
    
    # 创建测试环境
    grid_map = np.zeros((20, 20))
    
    # 添加障碍物
    grid_map[8:12, 8] = 1    # 垂直墙
    grid_map[10, 5:12] = 1   # 水平墙
    grid_map[3:6, 3:6] = 1   # 小方块
    
    start = (2, 2)
    goal = (17, 17)
    
    print(f"测试环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 创建ACO参数
    params = create_aco_params(
        max_iterations=80,
        num_ants=15,
        alpha=1.0,
        beta=2.0,
        evaporation_rate=0.1,
        q0=0.9,
        timeout=15.0
    )
    
    # 创建ACO规划器
    aco_planner = ACOPlanner(start, goal, grid_map, params)
    
    # 执行规划
    print("🚀 开始ACO规划...")
    result = aco_planner.plan()
    
    # 输出结果
    print(f"✅ ACO规划完成!")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   计算时间: {result.computation_time:.3f}秒")
    print(f"   迭代次数: {result.iterations}")
    
    # 可视化结果
    print("🎨 可视化结果...")
    aco_planner.visualize(result, show_process=True)
    
    # 测试路径指标
    if result.path:
        metrics = PathPlanningUtils.calculate_path_metrics(result.path)
        print(f"📊 路径指标:")
        print(f"   欧几里得长度: {metrics['euclidean_length']:.2f}")
        print(f"   平滑度: {metrics['smoothness']:.2f}")
        print(f"   转弯次数: {metrics['turn_count']}")
    
    return result


if __name__ == "__main__":
    test_aco_planner()