"""
修复后的RRT*路径规划算法
适用于海冰分割系统的网格地图环境
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt

# 尝试导入基础类，如果不存在则定义简化版本
try:
    from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils
except ImportError:
    # 定义简化的基础类
    class PathPlannerBase:
        def __init__(self, start, goal, grid_map, params=None):
            self.start = start
            self.goal = goal
            self.grid_map = np.array(grid_map)
            self.map_height, self.map_width = self.grid_map.shape
            self.params = params

    class PlanningResult:
        def __init__(self, path=None, success=False, cost=0.0, computation_time=0.0,
                     iterations=0, algorithm_name="", additional_data=None):
            self.path = path
            self.success = success
            self.cost = cost
            self.computation_time = computation_time
            self.iterations = iterations
            self.algorithm_name = algorithm_name
            self.additional_data = additional_data or {}

    class AlgorithmParams:
        def __init__(self, max_iterations=1000, step_size=1.0, goal_bias=0.1, timeout=30.0):
            self.max_iterations = max_iterations
            self.step_size = step_size
            self.goal_bias = goal_bias
            self.timeout = timeout
            self.algorithm_specific = {}

    class PathPlanningUtils:
        @staticmethod
        def distance(p1, p2):
            return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

        @staticmethod
        def is_valid_point(point, grid_map):
            row, col = point
            height, width = grid_map.shape
            if row < 0 or row >= height or col < 0 or col >= width:
                return False
            return grid_map[row, col] == 0

        @staticmethod
        def check_collision_line(start, end, grid_map, step_size=0.5):
            start_row, start_col = start
            end_row, end_col = end
            distance = np.sqrt((end_row - start_row)**2 + (end_col - start_col)**2)
            if distance == 0:
                return False
            num_steps = int(distance / step_size) + 1
            for i in range(num_steps + 1):
                t = i / num_steps if num_steps > 0 else 0
                check_row = int(start_row + t * (end_row - start_row))
                check_col = int(start_col + t * (end_col - start_col))
                if not PathPlanningUtils.is_valid_point((check_row, check_col), grid_map):
                    return True
            return False


class RRTStarNode:
    """RRT*树节点"""
    def __init__(self, position: Tuple[float, float], parent: Optional['RRTStarNode'] = None):
        self.position = position  # (row, col) 位置
        self.parent = parent      # 父节点
        self.cost = 0.0          # 从起点到该节点的代价

        if parent:
            self.cost = parent.cost + PathPlanningUtils.distance(parent.position, position)

    def set_parent(self, new_parent: 'RRTStarNode'):
        """设置新的父节点并更新代价"""
        self.parent = new_parent
        if new_parent:
            self.cost = new_parent.cost + PathPlanningUtils.distance(new_parent.position, self.position)
        else:
            self.cost = 0.0


class RRTStarPlanner(PathPlannerBase):
    """
    修复后的RRT*路径规划器

    主要改进：
    1. 实现正确的重连线优化
    2. 动态搜索半径调整
    3. 改进的收敛性判断
    4. 统一的接口和数据结构
    """

    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int],
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化RRT*规划器

        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)

        # RRT*特定参数
        self.max_iter = params.max_iterations if params else 1000
        self.step_size = params.step_size if params else 1.0
        self.goal_bias = params.goal_bias if params else 0.1

        # RRT*特有参数
        self.initial_radius = 2.0
        self.radius_decay = 0.95
        self.min_radius = 0.5

        # 从算法特定参数中获取额外配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.step_size = params.algorithm_specific.get('step_size', self.step_size)
            self.goal_bias = params.algorithm_specific.get('goal_bias', self.goal_bias)
            self.initial_radius = params.algorithm_specific.get('initial_radius', self.initial_radius)
            self.radius_decay = params.algorithm_specific.get('radius_decay', self.radius_decay)
            self.min_radius = params.algorithm_specific.get('min_radius', self.min_radius)

        # 树结构
        self.nodes = []  # 存储所有节点
        self.start_node = RRTStarNode(start)
        self.nodes.append(self.start_node)

        # 搜索半径
        self.search_radius = self.initial_radius

        # 最优解跟踪
        self.best_goal_node = None
        self.best_cost = float('inf')

        # 用于可视化的探索树边
        self.exploration_edges = []

    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "RRT*"

    def plan(self) -> PlanningResult:
        """
        执行RRT*路径规划

        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0

        try:
            for i in range(self.max_iter):
                self.iterations = i + 1

                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break

                # 动态调整搜索半径
                self._update_search_radius()

                # 随机采样
                if random.random() < self.goal_bias:
                    sample_point = self.goal
                else:
                    sample_point = self._random_sample()

                # 找到最近节点
                nearest_node = self._find_nearest_node(sample_point)

                # 扩展树
                new_node = self._extend_tree(nearest_node, sample_point)

                if new_node is None:
                    continue

                # 选择最优父节点
                self._choose_parent(new_node)

                # 添加到树中
                self.nodes.append(new_node)
                self.exploration_edges.append((new_node.parent.position, new_node.position))

                # 重连线优化
                self._rewire(new_node)

                # 检查是否到达目标
                if self._is_goal_reached(new_node):
                    goal_cost = new_node.cost + PathPlanningUtils.distance(new_node.position, self.goal)
                    if goal_cost < self.best_cost:
                        self.best_cost = goal_cost
                        self.best_goal_node = new_node

                # 如果找到了路径且已经优化了一段时间，可以提前结束
                if (self.best_goal_node is not None and
                    i > 200 and  # 至少运行200次迭代
                    i % 100 == 0):  # 每100次迭代检查一次
                    # 如果最近100次迭代没有显著改进，可以结束
                    if hasattr(self, '_last_best_cost'):
                        improvement = (self._last_best_cost - self.best_cost) / self._last_best_cost
                        if improvement < 0.01:  # 改进小于1%
                            break
                    self._last_best_cost = self.best_cost

            # 构建结果
            computation_time = time.time() - self.start_time

            if self.best_goal_node is not None:
                path = self._extract_path(self.best_goal_node)
                return PlanningResult(
                    path=path,
                    success=True,
                    cost=self.best_cost,
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={'exploration_edges': self.exploration_edges}
                )
            else:
                # 未找到完整路径，但提供最接近目标的部分路径
                closest_node = None
                min_distance = float('inf')
                for node in self.nodes:
                    distance = PathPlanningUtils.distance(node.position, self.goal)
                    if distance < min_distance:
                        min_distance = distance
                        closest_node = node

                # 提取到最接近节点的部分路径
                partial_path = None
                if closest_node:
                    partial_path = self._extract_path(closest_node)

                return PlanningResult(
                    path=partial_path,  # 提供部分路径
                    success=False,
                    cost=float('inf'),
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={
                        'exploration_edges': self.exploration_edges,
                        'closest_distance': min_distance,
                        'partial_path_available': partial_path is not None
                    }
                )

        except Exception as e:
            computation_time = time.time() - self.start_time if self.start_time else 0.0
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={
                    'error': str(e),
                    'exploration_edges': self.exploration_edges
                }
            )

    def _update_search_radius(self):
        """动态更新搜索半径"""
        self.search_radius = max(self.min_radius, self.search_radius * self.radius_decay)

    def _random_sample(self) -> Tuple[float, float]:
        """随机采样"""
        row = random.uniform(0, self.map_height - 1)
        col = random.uniform(0, self.map_width - 1)
        return (row, col)

    def _find_nearest_node(self, sample_point: Tuple[float, float]) -> RRTStarNode:
        """找到树中距离采样点最近的节点"""
        min_dist = float('inf')
        nearest_node = self.start_node

        for node in self.nodes:
            dist = PathPlanningUtils.distance(node.position, sample_point)
            if dist < min_dist:
                min_dist = dist
                nearest_node = node

        return nearest_node

    def _extend_tree(self, nearest_node: RRTStarNode,
                    sample_point: Tuple[float, float]) -> Optional[RRTStarNode]:
        """从最近节点向采样点扩展树"""
        # 计算方向和距离
        direction = np.array(sample_point) - np.array(nearest_node.position)
        distance = np.linalg.norm(direction)

        if distance == 0:
            return None

        # 限制步长
        if distance > self.step_size:
            direction = direction / distance * self.step_size

        # 计算新位置
        new_position = tuple(np.array(nearest_node.position) + direction)

        # 检查新位置是否有效
        if not self._is_valid_position(new_position):
            return None

        # 检查路径是否无碰撞
        if self._check_collision_path(nearest_node.position, new_position):
            return None

        # 创建新节点（暂时以nearest_node为父节点）
        new_node = RRTStarNode(new_position, nearest_node)
        return new_node

    def _choose_parent(self, new_node: RRTStarNode):
        """为新节点选择最优父节点"""
        # 获取搜索半径内的所有节点
        nearby_nodes = self._get_nodes_in_radius(new_node.position, self.search_radius)

        min_cost = new_node.cost
        best_parent = new_node.parent

        for node in nearby_nodes:
            if node == new_node:
                continue

            # 计算通过该节点的潜在代价
            potential_cost = node.cost + PathPlanningUtils.distance(node.position, new_node.position)

            # 如果代价更低且路径无碰撞
            if (potential_cost < min_cost and
                not self._check_collision_path(node.position, new_node.position)):
                min_cost = potential_cost
                best_parent = node

        # 更新父节点
        if best_parent != new_node.parent:
            new_node.set_parent(best_parent)

    def _rewire(self, new_node: RRTStarNode):
        """重连线优化：检查是否可以通过新节点改进附近节点的路径"""
        nearby_nodes = self._get_nodes_in_radius(new_node.position, self.search_radius)

        for node in nearby_nodes:
            if node == new_node or node == new_node.parent:
                continue

            # 计算通过新节点的潜在代价
            potential_cost = new_node.cost + PathPlanningUtils.distance(new_node.position, node.position)

            # 如果代价更低且路径无碰撞
            if (potential_cost < node.cost and
                not self._check_collision_path(new_node.position, node.position)):
                # 更新父节点
                node.set_parent(new_node)
                # 递归更新所有子节点的代价
                self._update_children_cost(node)

    def _get_nodes_in_radius(self, center: Tuple[float, float],
                           radius: float) -> List[RRTStarNode]:
        """获取指定半径内的所有节点"""
        nearby_nodes = []
        for node in self.nodes:
            if PathPlanningUtils.distance(node.position, center) <= radius:
                nearby_nodes.append(node)
        return nearby_nodes

    def _update_children_cost(self, node: RRTStarNode):
        """递归更新节点的所有子节点的代价"""
        for child_node in self.nodes:
            if child_node.parent == node:
                old_cost = child_node.cost
                child_node.cost = node.cost + PathPlanningUtils.distance(node.position, child_node.position)
                # 如果代价发生变化，递归更新子节点
                if abs(child_node.cost - old_cost) > 1e-6:
                    self._update_children_cost(child_node)

    def _is_valid_position(self, position: Tuple[float, float]) -> bool:
        """检查位置是否有效"""
        row, col = position

        # 检查边界
        if row < 0 or row >= self.map_height or col < 0 or col >= self.map_width:
            return False

        # 检查障碍物
        grid_row, grid_col = int(row), int(col)
        if (0 <= grid_row < self.map_height and 0 <= grid_col < self.map_width):
            return self.grid_map[grid_row, grid_col] == 0

        return False

    def _check_collision_path(self, start: Tuple[float, float],
                             end: Tuple[float, float]) -> bool:
        """检查路径是否与障碍物碰撞"""
        return PathPlanningUtils.check_collision_line(start, end, self.grid_map, 0.5)

    def _is_goal_reached(self, node: RRTStarNode) -> bool:
        """检查是否到达目标"""
        distance = PathPlanningUtils.distance(node.position, self.goal)
        if distance <= self.step_size:
            # 检查到目标的路径是否无碰撞
            return not self._check_collision_path(node.position, self.goal)
        return False

    def _extract_path(self, goal_node: RRTStarNode) -> List[Tuple[int, int]]:
        """提取路径"""
        path = []
        current_node = goal_node

        # 从目标节点回溯到起点
        while current_node is not None:
            # 转换为整数坐标
            grid_pos = (int(round(current_node.position[0])),
                       int(round(current_node.position[1])))
            path.append(grid_pos)
            current_node = current_node.parent

        # 确保路径以目标点结束
        goal_grid = (int(round(self.goal[0])), int(round(self.goal[1])))
        if path and path[0] != goal_grid:
            path.insert(0, goal_grid)

        # 反转路径（从起点到终点）
        path.reverse()

        return path


def create_rrt_star_params(max_iterations: int = 1000, step_size: float = 1.0,
                          goal_bias: float = 0.1, timeout: float = 30.0,
                          initial_radius: float = 2.0, radius_decay: float = 0.95,
                          min_radius: float = 0.5) -> AlgorithmParams:
    """
    创建RRT*算法参数

    Args:
        max_iterations: 最大迭代次数
        step_size: 步长
        goal_bias: 目标偏置概率
        timeout: 超时时间
        initial_radius: 初始搜索半径
        radius_decay: 半径衰减率
        min_radius: 最小搜索半径

    Returns:
        AlgorithmParams: 算法参数
    """
    params = AlgorithmParams(max_iterations, step_size, goal_bias, timeout)
    params.algorithm_specific = {
        'max_iterations': max_iterations,
        'step_size': step_size,
        'goal_bias': goal_bias,
        'initial_radius': initial_radius,
        'radius_decay': radius_decay,
        'min_radius': min_radius
    }
    return params


def test_rrt_star_planner():
    """测试RRT*规划器"""
    print("🧪 测试RRT*规划器...")

    # 创建测试环境
    grid_map = np.zeros((20, 20))
    grid_map[5:15, 8] = 1  # 添加障碍物
    grid_map[10, 5:15] = 1

    start = (1, 1)
    goal = (18, 18)

    print(f"测试环境: {grid_map.shape}, 起点: {start}, 终点: {goal}")

    # 创建RRT*规划器
    params = create_rrt_star_params(
        max_iterations=1000,
        step_size=1.5,
        goal_bias=0.15,
        initial_radius=3.0,
        radius_decay=0.98,
        min_radius=1.0
    )
    planner = RRTStarPlanner(start, goal, grid_map, params)

    # 执行规划
    result = planner.plan()

    # 输出结果
    print(f"✅ RRT*规划完成:")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   时间: {result.computation_time:.3f}s")
    print(f"   迭代次数: {result.iterations}")

    if result.success and result.path:
        print(f"   起点: {result.path[0]}")
        print(f"   终点: {result.path[-1]}")

    return result


if __name__ == "__main__":
    test_rrt_star_planner()