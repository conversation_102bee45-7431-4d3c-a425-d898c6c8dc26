import numpy as np
import matplotlib.pyplot as plt
from random import random

class Node:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.parent = None
        self.cost = 0

class RRTstar:
    def __init__(self, start, goal, grid_map, step_size=0.5, max_iterations=1000, goal_sample_rate=0.1, search_radius=1.0):
        self.start = Node(start[0], start[1])
        self.goal = Node(goal[0], goal[1])
        self.grid_map = grid_map
        self.map_size = grid_map.shape
        self.step_size = step_size
        self.max_iterations = max_iterations
        self.goal_sample_rate = goal_sample_rate
        self.search_radius = search_radius
        self.nodes = [self.start]
        self.iterations = 0

    
    def plan(self):
        for iteration in range(self.max_iterations):
            self.iterations = iteration + 1

            # 随机采样
            if random() < self.goal_sample_rate:
                random_point = (self.goal.x, self.goal.y)
            else:
                random_point = (random() * self.map_size[0], random() * self.map_size[1])

            # 找到最近节点
            nearest_node = self._get_nearest_node(random_point)

            # 扩展
            new_node = self._extend(nearest_node, random_point)
            if new_node is None:
                continue

            # 选择父节点
            self._choose_parent(new_node)

            # 添加新节点到树中
            self.nodes.append(new_node)

            # 重新布线
            self._rewire(new_node)

            # 检查是否到达目标
            if self._distance((new_node.x, new_node.y), (self.goal.x, self.goal.y)) < self.step_size:
                if not self._check_collision_line((new_node.x, new_node.y), (self.goal.x, self.goal.y)):
                    self.goal.parent = new_node
                    self.goal.cost = new_node.cost + self._distance((new_node.x, new_node.y), (self.goal.x, self.goal.y))
                    return self._extract_path()

        return None
    
    def _get_nearest_node(self, point):
        distances = [self._distance((node.x, node.y), point) for node in self.nodes]
        return self.nodes[np.argmin(distances)]
    
    def _extend(self, nearest_node, random_point):
        dx, dy = random_point[0] - nearest_node.x, random_point[1] - nearest_node.y
        dist = self._distance((nearest_node.x, nearest_node.y), random_point)

        if dist == 0:
            return None

        # 计算新节点位置
        new_x = nearest_node.x + (dx * self.step_size / dist)
        new_y = nearest_node.y + (dy * self.step_size / dist)

        # 检查新节点是否有效
        if not self._is_valid_point((new_x, new_y)) or self._check_collision((new_x, new_y)):
            return None

        # 检查路径是否无碰撞
        if self._check_collision_line((nearest_node.x, nearest_node.y), (new_x, new_y)):
            return None

        new_node = Node(new_x, new_y)
        new_node.parent = nearest_node
        new_node.cost = nearest_node.cost + self._distance((nearest_node.x, nearest_node.y), (new_x, new_y))
        return new_node
    
    def _choose_parent(self, new_node):
        nearby_nodes = self._get_nodes_in_radius(new_node, self.search_radius)
        min_cost = new_node.cost
        min_parent = new_node.parent

        for node in nearby_nodes:
            if node == new_node.parent:
                continue

            d = self._distance((node.x, node.y), (new_node.x, new_node.y))
            potential_cost = node.cost + d

            if potential_cost < min_cost and not self._check_collision_line((node.x, node.y), (new_node.x, new_node.y)):
                min_cost = potential_cost
                min_parent = node

        new_node.parent = min_parent
        new_node.cost = min_cost
    
    def _rewire(self, new_node):
        nearby_nodes = self._get_nodes_in_radius(new_node, self.search_radius)

        for node in nearby_nodes:
            if node == new_node.parent or node == new_node:
                continue

            d = self._distance((new_node.x, new_node.y), (node.x, node.y))
            potential_cost = new_node.cost + d

            if potential_cost < node.cost and not self._check_collision_line((new_node.x, new_node.y), (node.x, node.y)):
                node.parent = new_node
                node.cost = potential_cost
                self._update_children_cost(node)
    
    def _get_nodes_in_radius(self, center_node, radius):
        return [node for node in self.nodes if self._distance(
            (node.x, node.y), (center_node.x, center_node.y)) <= radius]
    
    def _update_children_cost(self, node):
        children = [n for n in self.nodes if n.parent == node]
        for child in children:
            child.cost = node.cost + self._distance((node.x, node.y), (child.x, child.y))
            self._update_children_cost(child)

    def _is_valid_point(self, point):
        x, y = point[0], point[1]
        return 0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]

    def _check_collision_line(self, start_point, end_point):
        dist = self._distance(start_point, end_point)
        if dist == 0:
            return False

        num_checks = int(dist / (self.step_size / 2))
        for i in range(num_checks + 1):
            t = i / max(1, num_checks)
            x = start_point[0] * (1 - t) + end_point[0] * t
            y = start_point[1] * (1 - t) + end_point[1] * t
            if self._check_collision((x, y)):
                return True
        return False
    
    def _distance(self, p1, p2):
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    def _check_collision(self, point):
        if not self._is_valid_point(point):
            return True
        x, y = int(point[0]), int(point[1])
        if 0 <= x < self.grid_map.shape[0] and 0 <= y < self.grid_map.shape[1]:
            return self.grid_map[x, y] != 0
        return True
    
    def _extract_path(self):
        path, current_node = [], self.goal
        while current_node is not None:
            path.append((current_node.x, current_node.y))
            current_node = current_node.parent
        return path[::-1]

    def get_closest_path_to_goal(self):
        """获取到目标最近的路径（用于失败时展示）"""
        if not self.nodes:
            return None, float('inf')

        # 找到距离目标最近的节点
        min_distance = float('inf')
        closest_node = None
        for node in self.nodes:
            dist = self._distance((node.x, node.y), (self.goal.x, self.goal.y))
            if dist < min_distance:
                min_distance = dist
                closest_node = node

        # 提取到最近节点的路径
        path = []
        current_node = closest_node
        while current_node is not None:
            path.append((current_node.x, current_node.y))
            current_node = current_node.parent

        return path[::-1], min_distance

def main():
    grid_map = [
        [0, 0, 0, 0, 0],
        [0, 1, 1, 1, 0],
        [0, 1, 0, 1, 0],
        [0, 1, 0, 0, 0],
        [0, 0, 0, 0, 0]
    ]
    
    start, goal = (0, 0), (4, 4)
    rrt_star = RRTstar(start, goal, grid_map, step_size=0.5, max_iterations=1000, goal_sample_rate=0.1, search_radius=1.0)
    path = rrt_star.plan()
    
    if path:
        print(f"找到路径，迭代次数：{rrt_star.iterations}")
    else:
        print("未找到路径")
    
    plt.figure(figsize=(8, 8))
    for obstacle in rrt_star.obstacles:
        plt.fill([obstacle[0], obstacle[2], obstacle[2], obstacle[0]], 
                 [obstacle[1], obstacle[1], obstacle[3], obstacle[3]], 'red', alpha=0.3)
    
    for node in rrt_star.nodes:
        if node.parent:
            plt.plot([node.x, node.parent.x], [node.y, node.parent.y], 'g-', alpha=0.3)
    
    if path:
        plt.plot(*zip(*path), 'b-', linewidth=2, label='Path')
    
    plt.plot(start[0], start[1], 'go', label='Start')
    plt.plot(goal[0], goal[1], 'ro', label='Goal')
    
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT* 路径规划')
    plt.show()

if __name__ == "__main__":
    main()