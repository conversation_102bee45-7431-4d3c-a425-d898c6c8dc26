"""
修复后的RRT路径规划算法
适用于海冰分割系统的网格地图环境
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt

from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils


class RRTNode:
    """RRT树节点"""
    def __init__(self, position: Tu<PERSON>[float, float], parent: Optional['RRTNode'] = None):
        self.position = position  # (row, col) 位置
        self.parent = parent      # 父节点
        self.cost = 0.0          # 从起点到该节点的代价
        
        if parent:
            self.cost = parent.cost + PathPlanningUtils.distance(parent.position, position)


class RRTPlanner(PathPlannerBase):
    """
    修复后的RRT路径规划器
    
    主要改进：
    1. 适配网格地图环境
    2. 改进的碰撞检测
    3. 统一的接口和数据结构
    4. 更好的错误处理
    """
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], 
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化RRT规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)
        
        # RRT特定参数
        self.max_iter = params.max_iterations if params else 1000
        self.step_size = params.step_size if params else 1.0
        self.goal_bias = params.goal_bias if params else 0.1
        
        # 从算法特定参数中获取额外配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.step_size = params.algorithm_specific.get('step_size', self.step_size)
            self.goal_bias = params.algorithm_specific.get('goal_bias', self.goal_bias)
        
        # 树结构
        self.tree = []  # 存储所有节点
        self.start_node = RRTNode(start)
        self.tree.append(self.start_node)
        
        # 用于可视化的探索树边
        self.exploration_edges = []
        
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "RRT"
    
    def plan(self) -> PlanningResult:
        """
        执行RRT路径规划
        
        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0
        
        try:
            for i in range(self.max_iter):
                self.iterations = i + 1
                
                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break
                
                # 随机采样
                if random.random() < self.goal_bias:
                    sample_point = self.goal
                else:
                    sample_point = self._random_sample()
                
                # 找到最近节点
                nearest_node = self._find_nearest_node(sample_point)
                
                # 扩展树
                new_node = self._extend_tree(nearest_node, sample_point)
                
                if new_node is None:
                    continue
                
                # 添加到树中
                self.tree.append(new_node)
                self.exploration_edges.append((nearest_node.position, new_node.position))
                
                # 检查是否到达目标
                if self._is_goal_reached(new_node):
                    # 找到路径
                    path = self._extract_path(new_node)
                    computation_time = time.time() - self.start_time
                    
                    return PlanningResult(
                        path=path,
                        success=True,
                        cost=new_node.cost,
                        computation_time=computation_time,
                        iterations=self.iterations,
                        algorithm_name=self.get_algorithm_name(),
                        additional_data={
                            'tree_size': len(self.tree),
                            'exploration_edges': self.exploration_edges.copy()
                        }
                    )
            
            # 未找到路径
            computation_time = time.time() - self.start_time
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={
                    'tree_size': len(self.tree),
                    'exploration_edges': self.exploration_edges.copy(),
                    'reason': 'Max iterations reached'
                }
            )
            
        except Exception as e:
            computation_time = time.time() - self.start_time
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={
                    'error': str(e),
                    'tree_size': len(self.tree)
                }
            )
    
    def _random_sample(self) -> Tuple[float, float]:
        """
        随机采样一个点
        
        Returns:
            Tuple: 随机点坐标 (row, col)
        """
        # 在地图范围内随机采样
        row = random.uniform(0, self.map_height - 1)
        col = random.uniform(0, self.map_width - 1)
        return (row, col)
    
    def _find_nearest_node(self, point: Tuple[float, float]) -> RRTNode:
        """
        找到树中距离给定点最近的节点
        
        Args:
            point: 目标点
            
        Returns:
            RRTNode: 最近的节点
        """
        min_distance = float('inf')
        nearest_node = self.start_node
        
        for node in self.tree:
            distance = PathPlanningUtils.distance(node.position, point)
            if distance < min_distance:
                min_distance = distance
                nearest_node = node
        
        return nearest_node
    
    def _extend_tree(self, nearest_node: RRTNode, 
                    sample_point: Tuple[float, float]) -> Optional[RRTNode]:
        """
        从最近节点向采样点扩展树
        
        Args:
            nearest_node: 最近节点
            sample_point: 采样点
            
        Returns:
            Optional[RRTNode]: 新节点，如果扩展失败则返回None
        """
        # 计算方向和距离
        direction = np.array(sample_point) - np.array(nearest_node.position)
        distance = np.linalg.norm(direction)
        
        if distance == 0:
            return None
        
        # 限制步长
        if distance > self.step_size:
            direction = direction / distance * self.step_size
        
        # 计算新位置
        new_position = tuple(np.array(nearest_node.position) + direction)
        
        # 检查新位置是否有效
        if not self._is_valid_position(new_position):
            return None
        
        # 检查路径是否无碰撞
        if self._check_collision_path(nearest_node.position, new_position):
            return None
        
        # 创建新节点
        new_node = RRTNode(new_position, nearest_node)
        return new_node
    
    def _is_valid_position(self, position: Tuple[float, float]) -> bool:
        """
        检查位置是否有效
        
        Args:
            position: 位置坐标
            
        Returns:
            bool: 是否有效
        """
        row, col = position
        
        # 检查边界
        if row < 0 or row >= self.map_height or col < 0 or col >= self.map_width:
            return False
        
        # 检查是否在障碍物上
        grid_row, grid_col = int(row), int(col)
        if (0 <= grid_row < self.map_height and 0 <= grid_col < self.map_width):
            return self.grid_map[grid_row, grid_col] == 0
        
        return False
    
    def _check_collision_path(self, start_pos: Tuple[float, float], 
                            end_pos: Tuple[float, float]) -> bool:
        """
        检查路径是否与障碍物碰撞
        
        Args:
            start_pos: 起始位置
            end_pos: 结束位置
            
        Returns:
            bool: True表示有碰撞
        """
        # 使用更密集的采样检查路径
        distance = PathPlanningUtils.distance(start_pos, end_pos)
        if distance == 0:
            return False
        
        # 计算检查点数量
        num_checks = max(int(distance * 2), 5)  # 至少检查5个点
        
        for i in range(num_checks + 1):
            t = i / num_checks
            check_pos = (
                start_pos[0] * (1 - t) + end_pos[0] * t,
                start_pos[1] * (1 - t) + end_pos[1] * t
            )
            
            if not self._is_valid_position(check_pos):
                return True
        
        return False
    
    def _is_goal_reached(self, node: RRTNode) -> bool:
        """
        检查是否到达目标
        
        Args:
            node: 当前节点
            
        Returns:
            bool: 是否到达目标
        """
        distance = PathPlanningUtils.distance(node.position, self.goal)
        
        # 如果距离足够近且路径无碰撞
        if distance <= self.step_size:
            return not self._check_collision_path(node.position, self.goal)
        
        return False
    
    def _extract_path(self, goal_node: RRTNode) -> List[Tuple[int, int]]:
        """
        从目标节点提取路径
        
        Args:
            goal_node: 目标节点
            
        Returns:
            List: 路径点列表
        """
        path = []
        current_node = goal_node
        
        # 从目标节点回溯到起点
        while current_node is not None:
            # 转换为整数坐标
            grid_pos = (int(round(current_node.position[0])), 
                       int(round(current_node.position[1])))
            path.append(grid_pos)
            current_node = current_node.parent
        
        # 确保路径以目标点结束
        goal_grid = (int(round(self.goal[0])), int(round(self.goal[1])))
        if path and path[0] != goal_grid:
            path.insert(0, goal_grid)
        
        # 反转路径（从起点到终点）
        path.reverse()
        
        return path
    
    def visualize(self, result: PlanningResult, show_process: bool = False, 
                  save_path: Optional[str] = None):
        """
        可视化RRT结果
        
        Args:
            result: 规划结果
            show_process: 是否显示探索过程
            save_path: 保存路径
        """
        plt.figure(figsize=(12, 10))
        
        # 显示地图
        plt.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制探索树
        if show_process and 'exploration_edges' in result.additional_data:
            edges = result.additional_data['exploration_edges']
            for parent_pos, child_pos in edges:
                plt.plot([parent_pos[1], child_pos[1]], 
                        [parent_pos[0], child_pos[0]], 
                        'cyan', alpha=0.4, linewidth=0.8)
        
        # 绘制路径
        if result.success and result.path:
            path_array = np.array(result.path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=4, label='RRT Path')
            plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=5)
        
        # 标记起点和终点
        plt.plot(self.start[1], self.start[0], 'go', markersize=15, label='Start')
        plt.plot(self.goal[1], self.goal[0], 'ro', markersize=15, label='Goal')
        
        # 设置标题
        status = "成功" if result.success else "失败"
        title = f'RRT算法 - {status}\n'
        title += f'代价: {result.cost:.2f}, 时间: {result.computation_time:.3f}s, 迭代: {result.iterations}'
        if 'tree_size' in result.additional_data:
            title += f', 树大小: {result.additional_data["tree_size"]}'
        
        plt.title(title, fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


def create_rrt_params(max_iterations: int = 1000, step_size: float = 1.0, 
                     goal_bias: float = 0.1, timeout: float = 30.0) -> AlgorithmParams:
    """
    创建RRT算法参数
    
    Args:
        max_iterations: 最大迭代次数
        step_size: 步长
        goal_bias: 目标偏置概率
        timeout: 超时时间
        
    Returns:
        AlgorithmParams: 算法参数
    """
    return AlgorithmParams(
        max_iterations=max_iterations,
        step_size=step_size,
        goal_bias=goal_bias,
        timeout=timeout,
        algorithm_specific={
            'max_iterations': max_iterations,
            'step_size': step_size,
            'goal_bias': goal_bias
        }
    )


def test_rrt_planner():
    """测试RRT规划器"""
    print("🧪 测试RRT路径规划器...")
    
    # 创建测试环境
    grid_map = np.zeros((30, 30))
    
    # 添加障碍物
    grid_map[10:20, 10] = 1    # 垂直墙
    grid_map[15, 5:15] = 1     # 水平墙
    grid_map[5:8, 5:8] = 1     # 小方块
    grid_map[22:25, 22:25] = 1 # 另一个小方块
    
    start = (2, 2)
    goal = (27, 27)
    
    print(f"测试环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 创建RRT参数
    params = create_rrt_params(
        max_iterations=2000,
        step_size=1.5,
        goal_bias=0.15,
        timeout=10.0
    )
    
    # 创建RRT规划器
    rrt_planner = RRTPlanner(start, goal, grid_map, params)
    
    # 执行规划
    print("🚀 开始RRT规划...")
    result = rrt_planner.plan()
    
    # 输出结果
    print(f"✅ RRT规划完成!")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   计算时间: {result.computation_time:.3f}秒")
    print(f"   迭代次数: {result.iterations}")
    
    if 'tree_size' in result.additional_data:
        print(f"   探索树大小: {result.additional_data['tree_size']}")
    
    # 可视化结果
    print("🎨 可视化结果...")
    rrt_planner.visualize(result, show_process=True)
    
    # 测试路径指标
    if result.path:
        metrics = PathPlanningUtils.calculate_path_metrics(result.path)
        print(f"📊 路径指标:")
        print(f"   欧几里得长度: {metrics['euclidean_length']:.2f}")
        print(f"   平滑度: {metrics['smoothness']:.2f}")
        print(f"   转弯次数: {metrics['turn_count']}")
    
    return result


if __name__ == "__main__":
    test_rrt_planner()