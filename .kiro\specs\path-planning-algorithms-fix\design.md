# 设计文档

## 概述

本设计文档描述了如何修复和集成多种路径规划算法（RRT、RRT*、ACO、PSO）到海冰分割系统中。当前系统存在算法实现不完整、接口不一致、错误处理不当等问题，需要进行全面的重构和优化。

## 架构设计

### 整体架构

```
海冰分割系统
├── 图像处理模块
├── 路径规划模块 (新设计)
│   ├── 基础接口层
│   ├── 算法实现层
│   │   ├── RRT算法
│   │   ├── RRT*算法  
│   │   ├── ACO算法
│   │   └── PSO算法
│   ├── 工具函数层
│   └── 可视化层
└── 结果分析模块
```

### 模块职责

1. **基础接口层**: 定义统一的算法接口和数据结构
2. **算法实现层**: 实现各种路径规划算法的核心逻辑
3. **工具函数层**: 提供通用的辅助函数（碰撞检测、路径验证等）
4. **可视化层**: 统一的结果展示和分析功能

## 组件和接口设计

### 1. 基础接口定义

```python
class PathPlannerBase:
    """路径规划算法基类"""
    def __init__(self, start, goal, grid_map, **kwargs):
        pass
    
    def plan(self):
        """执行路径规划，返回标准格式结果"""
        pass
    
    def get_metrics(self):
        """获取算法性能指标"""
        pass
    
    def visualize(self, show_process=False):
        """可视化结果"""
        pass
```

### 2. 标准数据结构

```python
@dataclass
class PlanningResult:
    """路径规划结果的标准数据结构"""
    path: List[Tuple[int, int]]  # 路径点列表 (row, col)
    success: bool                # 是否成功找到路径
    cost: float                  # 路径代价
    computation_time: float      # 计算时间
    iterations: int              # 迭代次数
    algorithm_name: str          # 算法名称
    additional_data: Dict        # 算法特定的额外数据
```

### 3. 工具函数接口

```python
class PathPlanningUtils:
    """路径规划工具函数集合"""
    
    @staticmethod
    def is_valid_point(point, grid_map):
        """检查点是否有效"""
        pass
    
    @staticmethod
    def check_collision_line(start, end, grid_map):
        """检查线段是否与障碍物碰撞"""
        pass
    
    @staticmethod
    def calculate_path_metrics(path, grid_map):
        """计算路径指标"""
        pass
    
    @staticmethod
    def smooth_path(path):
        """路径平滑处理"""
        pass
```

## 算法具体设计

### 1. RRT算法重构

**问题分析**:
- 当前RRT实现混合了多个不同的类定义
- 缺少proper的碰撞检测
- 没有统一的接口

**设计方案**:
```python
class RRTPlanner(PathPlannerBase):
    def __init__(self, start, goal, grid_map, max_iter=1000, step_size=1.0, goal_bias=0.1):
        self.start = start
        self.goal = goal
        self.grid_map = grid_map
        self.max_iter = max_iter
        self.step_size = step_size
        self.goal_bias = goal_bias
        self.tree = {start: None}  # 节点: 父节点
        
    def plan(self):
        """执行RRT规划"""
        for i in range(self.max_iter):
            # 随机采样
            if random.random() < self.goal_bias:
                rand_point = self.goal
            else:
                rand_point = self._random_sample()
            
            # 找最近节点并扩展
            nearest = self._find_nearest(rand_point)
            new_point = self._extend(nearest, rand_point)
            
            if new_point and self._is_collision_free(nearest, new_point):
                self.tree[new_point] = nearest
                
                # 检查是否到达目标
                if self._distance(new_point, self.goal) < self.step_size:
                    return self._extract_path(new_point)
        
        return None
```

### 2. RRT*算法重构

**问题分析**:
- 当前实现缺少proper的重连线逻辑
- 成本计算不准确
- 没有动态半径调整

**设计方案**:
```python
class RRTStarPlanner(PathPlannerBase):
    def __init__(self, start, goal, grid_map, max_iter=1000, step_size=1.0, 
                 goal_bias=0.1, rewire_radius=2.0):
        super().__init__(start, goal, grid_map)
        self.max_iter = max_iter
        self.step_size = step_size
        self.goal_bias = goal_bias
        self.rewire_radius = rewire_radius
        self.nodes = {start: {'parent': None, 'cost': 0.0}}
        
    def plan(self):
        """执行RRT*规划"""
        for i in range(self.max_iter):
            # 采样和扩展
            rand_point = self._sample()
            nearest = self._find_nearest(rand_point)
            new_point = self._extend(nearest, rand_point)
            
            if new_point and self._is_collision_free(nearest, new_point):
                # 选择最优父节点
                self._choose_parent(new_point)
                self.nodes[new_point] = {'parent': best_parent, 'cost': best_cost}
                
                # 重连线优化
                self._rewire(new_point)
                
                # 检查目标
                if self._distance(new_point, self.goal) < self.step_size:
                    return self._extract_optimal_path()
        
        return None
```

### 3. ACO算法重构

**问题分析**:
- 当前实现过于复杂，信息素更新机制不清晰
- 缺少proper的启发式函数
- 收敛判断不准确

**设计方案**:
```python
class ACOPlanner(PathPlannerBase):
    def __init__(self, start, goal, grid_map, n_ants=20, max_iter=100, 
                 alpha=1.0, beta=2.0, evaporation=0.1):
        super().__init__(start, goal, grid_map)
        self.n_ants = n_ants
        self.max_iter = max_iter
        self.alpha = alpha  # 信息素重要性
        self.beta = beta    # 启发式重要性
        self.evaporation = evaporation
        self.pheromone = self._init_pheromone()
        
    def plan(self):
        """执行ACO规划"""
        best_path = None
        best_cost = float('inf')
        
        for iteration in range(self.max_iter):
            # 所有蚂蚁构建路径
            paths = []
            for ant in range(self.n_ants):
                path = self._construct_path()
                if path:
                    paths.append(path)
                    cost = self._calculate_cost(path)
                    if cost < best_cost:
                        best_cost = cost
                        best_path = path
            
            # 更新信息素
            self._update_pheromone(paths)
            
            # 检查收敛
            if self._check_convergence():
                break
                
        return best_path
```

### 4. PSO算法重构

**问题分析**:
- 当前实现的粒子表示不合理
- 速度更新机制有问题
- 缺少约束处理

**设计方案**:
```python
class PSOPlanner(PathPlannerBase):
    def __init__(self, start, goal, grid_map, n_particles=30, max_iter=100,
                 w=0.7, c1=1.4, c2=1.4, n_waypoints=8):
        super().__init__(start, goal, grid_map)
        self.n_particles = n_particles
        self.max_iter = max_iter
        self.w = w      # 惯性权重
        self.c1 = c1    # 个体学习因子
        self.c2 = c2    # 社会学习因子
        self.n_waypoints = n_waypoints
        self.particles = self._init_particles()
        
    def plan(self):
        """执行PSO规划"""
        for iteration in range(self.max_iter):
            for particle in self.particles:
                # 评估适应度
                fitness = self._evaluate_fitness(particle)
                
                # 更新个体最优
                if fitness < particle['best_fitness']:
                    particle['best_fitness'] = fitness
                    particle['best_position'] = particle['position'].copy()
                
                # 更新全局最优
                if fitness < self.global_best_fitness:
                    self.global_best_fitness = fitness
                    self.global_best_position = particle['position'].copy()
            
            # 更新粒子速度和位置
            self._update_particles()
            
        return self._waypoints_to_path(self.global_best_position)
```

## 数据模型

### 1. 网格地图表示
```python
# 统一使用numpy数组表示
# 0: 可通行区域
# 1: 障碍物
# 坐标系: (row, col) 格式，原点在左上角
grid_map: np.ndarray[int]  # shape: (height, width)
```

### 2. 路径表示
```python
# 统一路径格式
path: List[Tuple[int, int]]  # [(row1, col1), (row2, col2), ...]
```

### 3. 算法参数
```python
@dataclass
class AlgorithmParams:
    """算法参数的标准结构"""
    max_iterations: int = 1000
    step_size: float = 1.0
    goal_bias: float = 0.1
    # 算法特定参数
    algorithm_specific: Dict = field(default_factory=dict)
```

## 错误处理

### 1. 输入验证
- 检查起点和终点是否在地图范围内
- 验证起点和终点不在障碍物上
- 检查地图格式和数据类型

### 2. 运行时错误处理
- 算法超时处理
- 内存不足处理
- 无解情况处理

### 3. 结果验证
- 路径连通性检查
- 碰撞检测验证
- 路径合理性验证

## 测试策略

### 1. 单元测试
- 每个算法类的独立测试
- 工具函数的单独测试
- 边界条件测试

### 2. 集成测试
- 算法与海冰分割系统的集成测试
- 不同算法结果的一致性测试
- 性能基准测试

### 3. 场景测试
- 简单环境测试
- 复杂障碍物环境测试
- 海冰实际数据测试

## 性能优化

### 1. 算法优化
- 使用KD-tree加速最近邻搜索
- 实现路径缓存机制
- 优化碰撞检测算法

### 2. 内存优化
- 使用生成器减少内存占用
- 实现增量式数据结构
- 及时释放不需要的数据

### 3. 并行化
- 支持多线程执行不同算法
- PSO和ACO的并行粒子/蚂蚁处理
- 批量路径验证

## 可视化设计

### 1. 统一可视化接口
```python
class PathPlanningVisualizer:
    def plot_result(self, result: PlanningResult, grid_map, show_process=False):
        """统一的结果可视化"""
        pass
    
    def compare_algorithms(self, results: List[PlanningResult], grid_map):
        """算法比较可视化"""
        pass
    
    def plot_metrics(self, results: List[PlanningResult]):
        """性能指标可视化"""
        pass
```

### 2. 可视化内容
- 地图和障碍物显示
- 路径轨迹绘制
- 算法探索过程动画
- 性能指标对比图表

## 集成方案

### 1. 与海冰分割系统集成
- 保持现有的图像处理流程
- 在路径规划阶段替换算法实现
- 保持结果输出格式兼容

### 2. 配置管理
- 支持算法参数配置文件
- 运行时算法选择
- 批量测试配置

### 3. 向后兼容
- 保持现有函数接口
- 渐进式迁移策略
- 旧版本结果对比验证