"""
修复后的粒子群优化算法用于路径规划
适配海冰分割路径规划系统
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt

# 尝试导入基础类，如果不存在则定义简化版本
try:
    from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils
except ImportError:
    # 定义简化的基础类
    class PathPlannerBase:
        def __init__(self, start, goal, grid_map, params=None):
            self.start = start
            self.goal = goal
            self.grid_map = np.array(grid_map)
            self.map_height, self.map_width = self.grid_map.shape
            self.params = params

    class PlanningResult:
        def __init__(self, path=None, success=False, cost=0.0, computation_time=0.0,
                     iterations=0, algorithm_name="", additional_data=None):
            self.path = path
            self.success = success
            self.cost = cost
            self.computation_time = computation_time
            self.iterations = iterations
            self.algorithm_name = algorithm_name
            self.additional_data = additional_data or {}

    class AlgorithmParams:
        def __init__(self, max_iterations=1000, step_size=1.0, goal_bias=0.1, timeout=30.0):
            self.max_iterations = max_iterations
            self.step_size = step_size
            self.goal_bias = goal_bias
            self.timeout = timeout
            self.algorithm_specific = {}

    class PathPlanningUtils:
        @staticmethod
        def distance(p1, p2):
            return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

        @staticmethod
        def is_valid_point(point, grid_map):
            row, col = point
            height, width = grid_map.shape
            if row < 0 or row >= height or col < 0 or col >= width:
                return False
            return grid_map[row, col] == 0

        @staticmethod
        def check_collision_line(start, end, grid_map, step_size=0.5):
            start_row, start_col = start
            end_row, end_col = end
            distance = np.sqrt((end_row - start_row)**2 + (end_col - start_col)**2)
            if distance == 0:
                return False
            num_steps = int(distance / step_size) + 1
            for i in range(num_steps + 1):
                t = i / num_steps if num_steps > 0 else 0
                check_row = int(start_row + t * (end_row - start_row))
                check_col = int(start_col + t * (end_col - start_col))
                if not PathPlanningUtils.is_valid_point((check_row, check_col), grid_map):
                    return True
            return False


class PSOParticle:
    """PSO粒子类"""
    def __init__(self, path_length: int, map_height: int, map_width: int,
                 start: Tuple[int, int], goal: Tuple[int, int]):
        self.path_length = path_length
        self.map_height = map_height
        self.map_width = map_width
        self.start = start
        self.goal = goal

        # 初始化路径（粒子位置）
        self.position = self._initialize_path()
        self.velocity = np.zeros_like(self.position)

        # 个体最优
        self.best_position = self.position.copy()
        self.best_fitness = float('inf')

    def _initialize_path(self) -> np.ndarray:
        """初始化路径"""
        path = np.zeros((self.path_length, 2))

        # 起点和终点固定
        path[0] = self.start
        path[-1] = self.goal

        # 中间点线性插值加随机扰动，确保不在障碍物中
        for i in range(1, self.path_length - 1):
            t = i / (self.path_length - 1)
            # 线性插值
            interpolated = (1 - t) * np.array(self.start) + t * np.array(self.goal)

            # 尝试找到一个不在障碍物中的位置
            valid_position_found = False
            attempts = 0
            max_attempts = 20

            while not valid_position_found and attempts < max_attempts:
                # 添加随机扰动
                noise_scale = min(self.map_height, self.map_width) * 0.15
                noise = np.random.normal(0, noise_scale, 2)
                candidate_pos = interpolated + noise

                # 确保在地图范围内
                candidate_pos[0] = np.clip(candidate_pos[0], 0, self.map_height - 1)
                candidate_pos[1] = np.clip(candidate_pos[1], 0, self.map_width - 1)

                # 检查是否在障碍物中
                row, col = int(round(candidate_pos[0])), int(round(candidate_pos[1]))
                if (0 <= row < self.map_height and 0 <= col < self.map_width):
                    if self.map_height > 0 and self.map_width > 0:  # 确保地图有效
                        # 这里需要访问地图，但PSOParticle没有直接访问权限
                        # 暂时使用插值位置，后续在适应度函数中处理
                        path[i] = candidate_pos
                        valid_position_found = True
                    else:
                        path[i] = interpolated
                        valid_position_found = True
                else:
                    attempts += 1

            if not valid_position_found:
                # 如果找不到有效位置，使用线性插值
                path[i] = interpolated
                path[i, 0] = np.clip(path[i, 0], 0, self.map_height - 1)
                path[i, 1] = np.clip(path[i, 1], 0, self.map_width - 1)

        return path

class PSOPlanner(PathPlannerBase):
    """
    修复后的粒子群优化路径规划器

    主要改进：
    1. 改进粒子约束处理
    2. 更好的适应度函数设计
    3. 路径平滑处理
    4. 统一的接口和数据结构
    """

    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int],
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化PSO规划器

        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)

        # PSO特定参数
        self.max_iter = params.max_iterations if params else 100
        self.num_particles = 30
        self.w = 0.9  # 惯性权重
        self.c1 = 2.0  # 个体学习因子
        self.c2 = 2.0  # 社会学习因子
        self.path_length = 15  # 路径点数量

        # 从算法特定参数中获取额外配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.num_particles = params.algorithm_specific.get('num_particles', self.num_particles)
            self.w = params.algorithm_specific.get('w', self.w)
            self.c1 = params.algorithm_specific.get('c1', self.c1)
            self.c2 = params.algorithm_specific.get('c2', self.c2)
            self.path_length = params.algorithm_specific.get('path_length', self.path_length)

        # 全局最优
        self.global_best_position = None
        self.global_best_fitness = float('inf')

        # 统计信息
        self.fitness_history = []

        # 初始化粒子群
        self.particles = []
        self._initialize_particles()

    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "PSO"

    def _initialize_particles(self):
        """初始化粒子群"""
        for _ in range(self.num_particles):
            particle = PSOParticle(self.path_length, self.map_height, self.map_width,
                                 self.start, self.goal)

            # 评估初始适应度
            fitness = self._evaluate_fitness(particle.position)
            particle.best_fitness = fitness

            # 更新全局最优
            if fitness < self.global_best_fitness:
                self.global_best_fitness = fitness
                self.global_best_position = particle.position.copy()

            self.particles.append(particle)

    def plan(self) -> PlanningResult:
        """
        执行PSO路径规划

        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0

        try:
            for iteration in range(self.max_iter):
                self.iterations = iteration + 1

                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break

                # 更新所有粒子
                for particle in self.particles:
                    self._update_particle(particle)

                # 记录适应度历史
                self.fitness_history.append(self.global_best_fitness)

                # 早期停止判断
                if (iteration > 50 and iteration % 20 == 0 and
                    len(self.fitness_history) >= 20):
                    recent_improvement = (self.fitness_history[-20] - self.global_best_fitness) / self.fitness_history[-20]
                    if recent_improvement < 0.01:  # 改进小于1%
                        break

            # 构建结果
            computation_time = time.time() - self.start_time

            if self.global_best_position is not None:
                # 转换为网格路径
                path = self._convert_to_grid_path(self.global_best_position)

                # 检查路径有效性
                if path and len(path) >= 2:
                    return PlanningResult(
                        path=path,
                        success=True,
                        cost=self.global_best_fitness,
                        computation_time=computation_time,
                        iterations=self.iterations,
                        algorithm_name=self.get_algorithm_name(),
                        additional_data={'fitness_history': self.fitness_history}
                    )

            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={'fitness_history': self.fitness_history}
            )

        except Exception as e:
            computation_time = time.time() - self.start_time if self.start_time else 0.0
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={'error': str(e)}
            )

    def _update_particle(self, particle: PSOParticle):
        """更新粒子位置和速度"""
        # 生成随机数
        r1 = np.random.random(particle.position.shape)
        r2 = np.random.random(particle.position.shape)

        # 更新速度
        cognitive_component = self.c1 * r1 * (particle.best_position - particle.position)
        social_component = self.c2 * r2 * (self.global_best_position - particle.position)
        particle.velocity = self.w * particle.velocity + cognitive_component + social_component

        # 限制速度
        max_velocity = min(self.map_height, self.map_width) * 0.05  # 减小最大速度
        particle.velocity = np.clip(particle.velocity, -max_velocity, max_velocity)

        # 保存原始位置
        old_position = particle.position.copy()

        # 更新位置
        new_position = particle.position + particle.velocity

        # 约束位置（保持起点和终点固定）
        new_position[0] = self.start  # 起点固定
        new_position[-1] = self.goal  # 终点固定

        # 约束中间点在地图范围内，并避免障碍物
        for i in range(1, len(new_position) - 1):
            # 边界约束
            new_position[i, 0] = np.clip(new_position[i, 0], 0, self.map_height - 1)
            new_position[i, 1] = np.clip(new_position[i, 1], 0, self.map_width - 1)

            # 检查是否在障碍物中
            row, col = int(round(new_position[i, 0])), int(round(new_position[i, 1]))
            if (0 <= row < self.map_height and 0 <= col < self.map_width and
                self.grid_map[row, col] == 1):
                # 如果新位置在障碍物中，尝试找到附近的可行位置
                valid_position = self._find_valid_position(new_position[i], old_position[i])
                new_position[i] = valid_position

        # 检查路径段是否穿越障碍物
        valid_path = self._repair_path(new_position)
        particle.position = valid_path

        # 评估新适应度
        fitness = self._evaluate_fitness(particle.position)

        # 如果新位置的适应度过高（可能穿越障碍物），考虑回退
        if fitness > 100000:  # 如果适应度过高，说明有严重碰撞
            # 以一定概率回退到原位置
            if random.random() < 0.7:  # 70%概率回退
                particle.position = old_position
                fitness = self._evaluate_fitness(particle.position)

        # 更新个体最优
        if fitness < particle.best_fitness:
            particle.best_fitness = fitness
            particle.best_position = particle.position.copy()

            # 更新全局最优
            if fitness < self.global_best_fitness:
                self.global_best_fitness = fitness
                self.global_best_position = particle.position.copy()

    def _find_valid_position(self, target_pos: np.ndarray, fallback_pos: np.ndarray) -> np.ndarray:
        """找到一个有效的位置（不在障碍物中）"""
        # 首先尝试目标位置周围的位置
        for radius in [1, 2, 3]:
            for angle in np.linspace(0, 2*np.pi, 8, endpoint=False):
                test_pos = target_pos + radius * np.array([np.cos(angle), np.sin(angle)])
                test_pos[0] = np.clip(test_pos[0], 0, self.map_height - 1)
                test_pos[1] = np.clip(test_pos[1], 0, self.map_width - 1)

                row, col = int(round(test_pos[0])), int(round(test_pos[1]))
                if (0 <= row < self.map_height and 0 <= col < self.map_width and
                    self.grid_map[row, col] == 0):
                    return test_pos

        # 如果找不到，返回原位置
        return fallback_pos

    def _repair_path(self, path: np.ndarray) -> np.ndarray:
        """修复路径，确保不穿越障碍物"""
        repaired_path = path.copy()

        # 检查每个路径段
        for i in range(len(path) - 1):
            if PathPlanningUtils.check_collision_line(path[i], path[i + 1], self.grid_map, 0.3):
                # 如果路径段穿越障碍物，尝试修复
                # 简单策略：在两点之间插入中间点，绕过障碍物
                start_point = path[i]
                end_point = path[i + 1]

                # 尝试几个不同的中间点
                best_mid_point = None
                best_cost = float('inf')

                for offset_x in [-2, -1, 0, 1, 2]:
                    for offset_y in [-2, -1, 0, 1, 2]:
                        mid_point = (start_point + end_point) / 2 + np.array([offset_x, offset_y])
                        mid_point[0] = np.clip(mid_point[0], 0, self.map_height - 1)
                        mid_point[1] = np.clip(mid_point[1], 0, self.map_width - 1)

                        # 检查中间点是否有效
                        row, col = int(round(mid_point[0])), int(round(mid_point[1]))
                        if (0 <= row < self.map_height and 0 <= col < self.map_width and
                            self.grid_map[row, col] == 0):

                            # 检查两个子路径段是否都无碰撞
                            if (not PathPlanningUtils.check_collision_line(start_point, mid_point, self.grid_map, 0.3) and
                                not PathPlanningUtils.check_collision_line(mid_point, end_point, self.grid_map, 0.3)):

                                cost = (PathPlanningUtils.distance(start_point, mid_point) +
                                       PathPlanningUtils.distance(mid_point, end_point))
                                if cost < best_cost:
                                    best_cost = cost
                                    best_mid_point = mid_point

                # 如果找到了好的中间点，更新路径
                if best_mid_point is not None and i + 1 < len(repaired_path):
                    # 将中间点插入到路径中（如果可能的话）
                    # 这里简化处理，直接调整当前点
                    repaired_path[i + 1] = best_mid_point

        return repaired_path

    def _evaluate_fitness(self, path: np.ndarray) -> float:
        """评估路径适应度"""
        if len(path) < 2:
            return float('inf')

        # 计算基础路径长度
        path_length = 0.0
        for i in range(len(path) - 1):
            path_length += PathPlanningUtils.distance(path[i], path[i + 1])

        # 障碍物惩罚
        obstacle_penalty = 0.0
        collision_penalty = 0.0

        # 检查路径点是否在障碍物中
        for point in path:
            row, col = int(round(point[0])), int(round(point[1]))
            if (0 <= row < self.map_height and 0 <= col < self.map_width):
                if self.grid_map[row, col] == 1:
                    collision_penalty += 10000.0  # 严重惩罚，但不是无穷大
            else:
                collision_penalty += 5000.0  # 超出边界的惩罚

        # 检查路径段是否穿越障碍物
        for i in range(len(path) - 1):
            if PathPlanningUtils.check_collision_line(path[i], path[i + 1], self.grid_map, 0.5):
                collision_penalty += 8000.0  # 严重惩罚穿越障碍物

        # 障碍物距离惩罚（鼓励远离障碍物）
        for point in path:
            row, col = int(round(point[0])), int(round(point[1]))
            if (0 <= row < self.map_height and 0 <= col < self.map_width):
                # 计算到最近障碍物的距离
                min_obstacle_dist = float('inf')
                for dr in range(-2, 3):  # 检查周围5x5区域
                    for dc in range(-2, 3):
                        check_row, check_col = row + dr, col + dc
                        if (0 <= check_row < self.map_height and
                            0 <= check_col < self.map_width and
                            self.grid_map[check_row, check_col] == 1):
                            dist = np.sqrt(dr*dr + dc*dc)
                            min_obstacle_dist = min(min_obstacle_dist, dist)

                # 如果距离障碍物太近，增加惩罚
                if min_obstacle_dist < 1.5:
                    obstacle_penalty += (1.5 - min_obstacle_dist) * 100.0

        # 平滑度惩罚
        smoothness_penalty = 0.0
        if len(path) >= 3:
            for i in range(1, len(path) - 1):
                # 计算角度变化
                v1 = path[i] - path[i - 1]
                v2 = path[i + 1] - path[i]
                if np.linalg.norm(v1) > 1e-6 and np.linalg.norm(v2) > 1e-6:
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle = np.arccos(cos_angle)
                    smoothness_penalty += angle * 2.0

        total_fitness = path_length + collision_penalty + obstacle_penalty + smoothness_penalty

        # 如果有严重碰撞，返回一个很大但有限的值
        if collision_penalty > 0:
            total_fitness = max(total_fitness, 50000.0)

        return total_fitness

    def _convert_to_grid_path(self, continuous_path: np.ndarray) -> List[Tuple[int, int]]:
        """将连续路径转换为网格路径"""
        grid_path = []

        for point in continuous_path:
            grid_row = int(round(point[0]))
            grid_col = int(round(point[1]))

            # 确保在地图范围内
            grid_row = max(0, min(self.map_height - 1, grid_row))
            grid_col = max(0, min(self.map_width - 1, grid_col))

            grid_point = (grid_row, grid_col)

            # 避免重复点
            if not grid_path or grid_path[-1] != grid_point:
                grid_path.append(grid_point)

        return grid_path

    def _extract_obstacles(self):
        """提取障碍物位置"""
        obstacles = []
        for i in range(self.grid_map.shape[0]):
            for j in range(self.grid_map.shape[1]):
                if self.grid_map[i, j] == 1:
                    obstacles.append((j, i))  # (x, y)格式
        return obstacles

    def initialize_particles(self):
        for _ in range(self.n_particles):
            # 生成随机路径点
            path_points = np.zeros((self.n_points, 2))
            path_points[0] = self.start
            path_points[-1] = self.goal
            
            # 在起点和终点之间生成随机中间点
            for i in range(1, self.n_points-1):
                valid_point = False
                attempts = 0
                while not valid_point and attempts < 100:  # 限制尝试次数
                    x = np.random.uniform(0, self.map_size[1]-1)  # 注意坐标系
                    y = np.random.uniform(0, self.map_size[0]-1)
                    if not self._check_collision((x, y)):
                        path_points[i] = [x, y]
                        valid_point = True
                    attempts += 1

                # 如果找不到有效点，使用线性插值
                if not valid_point:
                    t = i / (self.n_points - 1)
                    path_points[i] = self.start * (1 - t) + self.goal * t
            
            particle = Particle(path_points, self.grid_map)
            self.evaluate_fitness(particle)
            self.particles.append(particle)
            
            if particle.current_fitness < self.global_best_fitness:
                self.global_best_fitness = particle.current_fitness
                self.global_best_position = np.copy(particle.position)

    def evaluate_fitness(self, particle):
        # 计算路径长度
        path_length = 0
        for i in range(len(particle.position)-1):
            path_length += np.linalg.norm(particle.position[i+1] - particle.position[i])
        
        # 计算碰撞惩罚
        collision_penalty = 0
        for i in range(len(particle.position)-1):
            if self._check_collision_line(particle.position[i], particle.position[i+1]):
                collision_penalty += 1000
        
        # 总适应度 = 路径长度 + 碰撞惩罚
        fitness = path_length + collision_penalty
        particle.current_fitness = fitness
        
        if fitness < particle.best_fitness:
            particle.best_fitness = fitness
            particle.best_position = np.copy(particle.position)

    def _check_collision(self, point):
        """检查点是否与障碍物碰撞"""
        x, y = int(point[0]), int(point[1])
        # 检查边界
        if x < 0 or x >= self.grid_map.shape[1] or y < 0 or y >= self.grid_map.shape[0]:
            return True
        # 检查障碍物
        return self.grid_map[y, x] == 1

    def _check_collision_line(self, start_point, end_point):
        # 检查两点之间的线段是否与障碍物相交
        num_checks = int(np.linalg.norm(end_point - start_point) * 2)
        for i in range(num_checks + 1):
            t = i / max(1, num_checks)
            point = start_point * (1 - t) + end_point * t
            if self._check_collision(point):
                return True
        return False

    def optimize(self):
        for iteration in range(self.max_iter):
            for particle in self.particles:
                # 更新速度
                r1, r2 = np.random.random(2)
                particle.velocity = (self.w * particle.velocity +
                                   self.c1 * r1 * (particle.best_position - particle.position) +
                                   self.c2 * r2 * (self.global_best_position - particle.position))
                
                # 更新位置（保持起点和终点不变）
                particle.position[1:-1] += particle.velocity[1:-1]
                
                # 确保路径点在地图范围内
                particle.position = np.clip(particle.position, [0, 0], [self.map_size[1]-1, self.map_size[0]-1])
                
                # 评估新位置
                self.evaluate_fitness(particle)
                
                # 更新全局最优
                if particle.current_fitness < self.global_best_fitness:
                    self.global_best_fitness = particle.current_fitness
                    self.global_best_position = np.copy(particle.position)
            
            if iteration % 10 == 0:
                print(f"迭代 {iteration}, 当前最优适应度: {self.global_best_fitness}")

        return self.global_best_position

    def get_path_as_points(self):
        """将PSO路径转换为点序列格式，与其他算法兼容"""
        if self.global_best_position is None:
            return None

        # 将连续路径点转换为离散网格点
        path_points = []
        for i in range(len(self.global_best_position) - 1):
            start_point = self.global_best_position[i]
            end_point = self.global_best_position[i + 1]

            # 在两点之间插值生成路径点
            distance = np.linalg.norm(end_point - start_point)
            num_points = max(2, int(distance * 2))  # 根据距离确定插值点数

            for j in range(num_points):
                t = j / (num_points - 1) if num_points > 1 else 0
                point = start_point * (1 - t) + end_point * t
                # 转换为整数坐标 (y, x) 格式以匹配其他算法
                grid_point = (int(round(point[1])), int(round(point[0])))
                if grid_point not in path_points:
                    path_points.append(grid_point)

        return path_points

def create_pso_params(max_iterations: int = 100, num_particles: int = 30,
                     w: float = 0.9, c1: float = 2.0, c2: float = 2.0,
                     path_length: int = 15, timeout: float = 30.0) -> AlgorithmParams:
    """
    创建PSO算法参数

    Args:
        max_iterations: 最大迭代次数
        num_particles: 粒子数量
        w: 惯性权重
        c1: 个体学习因子
        c2: 社会学习因子
        path_length: 路径点数量
        timeout: 超时时间

    Returns:
        AlgorithmParams: 算法参数
    """
    params = AlgorithmParams(max_iterations, 1.0, 0.1, timeout)
    params.algorithm_specific = {
        'max_iterations': max_iterations,
        'num_particles': num_particles,
        'w': w,
        'c1': c1,
        'c2': c2,
        'path_length': path_length
    }
    return params


def test_pso_planner():
    """测试PSO规划器"""
    print("🧪 测试PSO规划器...")

    # 创建测试环境
    grid_map = np.zeros((20, 20))
    grid_map[5:15, 8] = 1  # 添加障碍物
    grid_map[10, 5:15] = 1

    start = (1, 1)
    goal = (18, 18)

    print(f"测试环境: {grid_map.shape}, 起点: {start}, 终点: {goal}")

    # 创建PSO规划器
    params = create_pso_params(
        max_iterations=100,
        num_particles=25,
        w=0.8,
        c1=2.0,
        c2=2.0,
        path_length=12
    )
    planner = PSOPlanner(start, goal, grid_map, params)

    # 执行规划
    result = planner.plan()

    # 输出结果
    print(f"✅ PSO规划完成:")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   时间: {result.computation_time:.3f}s")
    print(f"   迭代次数: {result.iterations}")

    if result.success and result.path:
        print(f"   起点: {result.path[0]}")
        print(f"   终点: {result.path[-1]}")

    return result


if __name__ == "__main__":
    test_pso_planner()