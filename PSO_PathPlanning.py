"""
重新编写的粒子群优化算法用于路径规划
适配海冰分割路径规划系统
"""

import numpy as np
import random
import time
from typing import List, Tuple, Optional

# 尝试导入基础类，如果不存在则定义简化版本
try:
    from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils
except ImportError:
    # 定义简化的基础类
    class PathPlannerBase:
        def __init__(self, start, goal, grid_map, params=None):
            self.start = start
            self.goal = goal
            self.grid_map = np.array(grid_map)
            self.map_height, self.map_width = self.grid_map.shape
            self.params = params
            
    class PlanningResult:
        def __init__(self, path=None, success=False, cost=0.0, computation_time=0.0, 
                     iterations=0, algorithm_name="", additional_data=None):
            self.path = path
            self.success = success
            self.cost = cost
            self.computation_time = computation_time
            self.iterations = iterations
            self.algorithm_name = algorithm_name
            self.additional_data = additional_data or {}
            
    class AlgorithmParams:
        def __init__(self, max_iterations=1000, step_size=1.0, goal_bias=0.1, timeout=30.0):
            self.max_iterations = max_iterations
            self.step_size = step_size
            self.goal_bias = goal_bias
            self.timeout = timeout
            self.algorithm_specific = {}
            
    class PathPlanningUtils:
        @staticmethod
        def distance(p1, p2):
            return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
        
        @staticmethod
        def is_valid_point(point, grid_map):
            row, col = int(point[0]), int(point[1])
            height, width = grid_map.shape
            if row < 0 or row >= height or col < 0 or col >= width:
                return False
            return grid_map[row, col] == 0
        
        @staticmethod
        def check_collision_line(start, end, grid_map, step_size=0.5):
            start_row, start_col = start
            end_row, end_col = end
            distance = np.sqrt((end_row - start_row)**2 + (end_col - start_col)**2)
            if distance == 0:
                return False
            num_steps = int(distance / step_size) + 1
            for i in range(num_steps + 1):
                t = i / num_steps if num_steps > 0 else 0
                check_row = int(start_row + t * (end_row - start_row))
                check_col = int(start_col + t * (end_col - start_col))
                if not PathPlanningUtils.is_valid_point((check_row, check_col), grid_map):
                    return True
            return False


class Particle:
    """粒子类 - 表示一条路径"""
    def __init__(self, num_waypoints: int, start: Tuple[int, int], goal: Tuple[int, int], 
                 map_height: int, map_width: int, grid_map: np.ndarray):
        self.num_waypoints = num_waypoints
        self.start = start
        self.goal = goal
        self.map_height = map_height
        self.map_width = map_width
        self.grid_map = grid_map
        
        # 粒子位置：中间航点的坐标
        self.position = self._initialize_position()
        self.velocity = np.zeros_like(self.position)
        
        # 个体最优
        self.best_position = self.position.copy()
        self.best_fitness = float('inf')
        
    def _initialize_position(self) -> np.ndarray:
        """初始化粒子位置（中间航点）"""
        # 只优化中间航点，起点和终点固定
        waypoints = np.zeros((self.num_waypoints, 2))
        
        for i in range(self.num_waypoints):
            # 在起点和终点之间线性插值，然后添加随机扰动
            t = (i + 1) / (self.num_waypoints + 1)
            base_point = np.array(self.start) * (1 - t) + np.array(self.goal) * t
            
            # 添加随机扰动，尝试找到可行位置
            for _ in range(50):
                noise = np.random.normal(0, min(self.map_height, self.map_width) * 0.2, 2)
                candidate = base_point + noise
                
                # 边界约束
                candidate[0] = np.clip(candidate[0], 1, self.map_height - 2)
                candidate[1] = np.clip(candidate[1], 1, self.map_width - 2)
                
                # 检查是否在可行区域
                if PathPlanningUtils.is_valid_point(candidate, self.grid_map):
                    waypoints[i] = candidate
                    break
            else:
                # 如果找不到可行位置，使用基础插值点
                waypoints[i] = base_point
                waypoints[i, 0] = np.clip(waypoints[i, 0], 1, self.map_height - 2)
                waypoints[i, 1] = np.clip(waypoints[i, 1], 1, self.map_width - 2)
        
        return waypoints
    
    def get_full_path(self) -> List[Tuple[int, int]]:
        """获取完整路径（包括起点和终点）"""
        full_path = [self.start]
        for waypoint in self.position:
            full_path.append((int(round(waypoint[0])), int(round(waypoint[1]))))
        full_path.append(self.goal)
        return full_path


class PSOPlanner(PathPlannerBase):
    """
    重新编写的粒子群优化路径规划器
    
    PSO算法原理：
    1. 每个粒子代表一条路径（通过中间航点定义）
    2. 粒子在解空间中搜索最优路径
    3. 通过个体经验和群体经验更新粒子位置
    """
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], 
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化PSO规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)
        
        # PSO特定参数
        self.max_iter = params.max_iterations if params else 100
        self.num_particles = 20
        self.num_waypoints = 6  # 中间航点数量
        self.w = 0.7  # 惯性权重
        self.c1 = 1.5  # 个体学习因子
        self.c2 = 1.5  # 社会学习因子
        
        # 从算法特定参数中获取额外配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.num_particles = params.algorithm_specific.get('num_particles', self.num_particles)
            self.w = params.algorithm_specific.get('w', self.w)
            self.c1 = params.algorithm_specific.get('c1', self.c1)
            self.c2 = params.algorithm_specific.get('c2', self.c2)
            self.num_waypoints = params.algorithm_specific.get('path_length', self.num_waypoints)
        
        # 粒子群
        self.particles = []
        
        # 全局最优
        self.global_best_position = None
        self.global_best_fitness = float('inf')
        self.global_best_path = None
        
        # 统计信息
        self.fitness_history = []
        
        # 初始化粒子群
        self._initialize_particles()
        
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "PSO"
    
    def _initialize_particles(self):
        """初始化粒子群"""
        for _ in range(self.num_particles):
            particle = Particle(self.num_waypoints, self.start, self.goal, 
                              self.map_height, self.map_width, self.grid_map)
            
            # 评估初始适应度
            fitness = self._evaluate_fitness(particle)
            particle.best_fitness = fitness
            
            # 更新全局最优
            if fitness < self.global_best_fitness:
                self.global_best_fitness = fitness
                self.global_best_position = particle.position.copy()
                self.global_best_path = particle.get_full_path()
            
            self.particles.append(particle)

    def plan(self) -> PlanningResult:
        """
        执行PSO路径规划

        Returns:
            PlanningResult: 规划结果
        """
        start_time = time.time()
        iterations = 0

        try:
            for iteration in range(self.max_iter):
                iterations = iteration + 1

                # 检查超时
                if time.time() - start_time > self.params.timeout:
                    break

                # 更新所有粒子
                for particle in self.particles:
                    self._update_particle(particle)

                # 记录适应度历史
                self.fitness_history.append(self.global_best_fitness)

                # 早期停止判断
                if (iteration > 30 and iteration % 10 == 0 and
                    len(self.fitness_history) >= 10):
                    recent_improvement = (self.fitness_history[-10] - self.global_best_fitness)
                    if recent_improvement < 0.1:  # 改进很小
                        break

            # 构建结果
            computation_time = time.time() - start_time

            if self.global_best_path and len(self.global_best_path) >= 2:
                return PlanningResult(
                    path=self.global_best_path,
                    success=True,
                    cost=self.global_best_fitness,
                    computation_time=computation_time,
                    iterations=iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={'fitness_history': self.fitness_history}
                )

            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={'fitness_history': self.fitness_history}
            )

        except Exception as e:
            computation_time = time.time() - start_time
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={'error': str(e)}
            )

    def _update_particle(self, particle: Particle):
        """更新粒子位置和速度"""
        # 生成随机数
        r1 = np.random.random(particle.position.shape)
        r2 = np.random.random(particle.position.shape)

        # 更新速度
        cognitive_component = self.c1 * r1 * (particle.best_position - particle.position)
        social_component = self.c2 * r2 * (self.global_best_position - particle.position)
        particle.velocity = self.w * particle.velocity + cognitive_component + social_component

        # 限制速度
        max_velocity = min(self.map_height, self.map_width) * 0.1
        particle.velocity = np.clip(particle.velocity, -max_velocity, max_velocity)

        # 更新位置
        particle.position += particle.velocity

        # 边界约束
        particle.position[:, 0] = np.clip(particle.position[:, 0], 1, self.map_height - 2)
        particle.position[:, 1] = np.clip(particle.position[:, 1], 1, self.map_width - 2)

        # 避免障碍物约束
        for i in range(len(particle.position)):
            row, col = int(round(particle.position[i, 0])), int(round(particle.position[i, 1]))
            if (0 <= row < self.map_height and 0 <= col < self.map_width and
                self.grid_map[row, col] == 1):
                # 如果在障碍物中，尝试移动到附近的可行位置
                particle.position[i] = self._find_valid_nearby_position(particle.position[i])

        # 额外检查：确保路径段不穿越障碍物
        full_path = particle.get_full_path()
        for i in range(len(full_path) - 1):
            if PathPlanningUtils.check_collision_line(full_path[i], full_path[i + 1], self.grid_map, 0.3):
                # 如果路径段穿越障碍物，调整中间航点
                if i > 0 and i < len(particle.position):
                    # 尝试调整对应的航点位置
                    waypoint_idx = i - 1 if i <= len(particle.position) else len(particle.position) - 1
                    particle.position[waypoint_idx] = self._find_detour_position(
                        full_path[i], full_path[i + 1], particle.position[waypoint_idx]
                    )

        # 评估新适应度
        fitness = self._evaluate_fitness(particle)

        # 更新个体最优
        if fitness < particle.best_fitness:
            particle.best_fitness = fitness
            particle.best_position = particle.position.copy()

            # 更新全局最优
            if fitness < self.global_best_fitness:
                self.global_best_fitness = fitness
                self.global_best_position = particle.position.copy()
                self.global_best_path = particle.get_full_path()

    def _find_valid_nearby_position(self, position: np.ndarray) -> np.ndarray:
        """在给定位置附近找到一个有效位置"""
        for radius in [1, 2, 3]:
            for angle in np.linspace(0, 2*np.pi, 8, endpoint=False):
                test_pos = position + radius * np.array([np.cos(angle), np.sin(angle)])
                test_pos[0] = np.clip(test_pos[0], 1, self.map_height - 2)
                test_pos[1] = np.clip(test_pos[1], 1, self.map_width - 2)

                if PathPlanningUtils.is_valid_point(test_pos, self.grid_map):
                    return test_pos

        # 如果找不到，返回原位置
        return position

    def _find_detour_position(self, start_point, end_point, current_waypoint):
        """找到一个绕行位置来避免路径段穿越障碍物"""
        # 计算起点和终点的中点
        mid_point = (np.array(start_point) + np.array(end_point)) / 2

        # 尝试在垂直方向上找到绕行点
        direction_vector = np.array(end_point) - np.array(start_point)
        if np.linalg.norm(direction_vector) > 0:
            # 计算垂直向量
            perpendicular = np.array([-direction_vector[1], direction_vector[0]])
            perpendicular = perpendicular / np.linalg.norm(perpendicular)

            # 尝试不同的偏移距离
            for offset_distance in [3, 5, 7, 10]:
                for direction in [1, -1]:  # 两个方向
                    detour_point = mid_point + direction * offset_distance * perpendicular

                    # 边界约束
                    detour_point[0] = np.clip(detour_point[0], 1, self.map_height - 2)
                    detour_point[1] = np.clip(detour_point[1], 1, self.map_width - 2)

                    # 检查绕行点是否可行
                    if PathPlanningUtils.is_valid_point(detour_point, self.grid_map):
                        # 检查到绕行点的路径段是否可行
                        if (not PathPlanningUtils.check_collision_line(start_point, detour_point, self.grid_map, 0.3) and
                            not PathPlanningUtils.check_collision_line(detour_point, end_point, self.grid_map, 0.3)):
                            return detour_point

        # 如果找不到好的绕行点，返回当前航点
        return current_waypoint

    def _evaluate_fitness(self, particle: Particle) -> float:
        """评估粒子适应度"""
        path = particle.get_full_path()

        if len(path) < 2:
            return float('inf')

        # 路径长度
        path_length = 0.0
        for i in range(len(path) - 1):
            path_length += PathPlanningUtils.distance(path[i], path[i + 1])

        # 碰撞惩罚
        collision_penalty = 0.0

        # 检查路径点是否在障碍物中
        for point in path:
            if not PathPlanningUtils.is_valid_point(point, self.grid_map):
                collision_penalty += 1000.0

        # 检查路径段是否穿越障碍物（更严格的检查）
        for i in range(len(path) - 1):
            if PathPlanningUtils.check_collision_line(path[i], path[i + 1], self.grid_map, 0.3):
                collision_penalty += 2000.0  # 增加惩罚力度

        # 平滑度惩罚
        smoothness_penalty = 0.0
        if len(path) >= 3:
            for i in range(1, len(path) - 1):
                v1 = np.array(path[i]) - np.array(path[i - 1])
                v2 = np.array(path[i + 1]) - np.array(path[i])
                if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle = np.arccos(cos_angle)
                    smoothness_penalty += angle * 5.0

        return path_length + collision_penalty + smoothness_penalty


def create_pso_params(max_iterations: int = 100, num_particles: int = 20,
                     w: float = 0.7, c1: float = 1.5, c2: float = 1.5,
                     path_length: int = 6, timeout: float = 30.0) -> AlgorithmParams:
    """
    创建PSO算法参数

    Args:
        max_iterations: 最大迭代次数
        num_particles: 粒子数量
        w: 惯性权重
        c1: 个体学习因子
        c2: 社会学习因子
        path_length: 中间航点数量
        timeout: 超时时间

    Returns:
        AlgorithmParams: 算法参数
    """
    params = AlgorithmParams(max_iterations, 1.0, 0.1, timeout)
    params.algorithm_specific = {
        'max_iterations': max_iterations,
        'num_particles': num_particles,
        'w': w,
        'c1': c1,
        'c2': c2,
        'path_length': path_length
    }
    return params


def test_pso_planner():
    """测试PSO规划器"""
    print("🧪 测试重新编写的PSO规划器...")

    # 创建测试环境
    grid_map = np.zeros((20, 20))
    grid_map[5:15, 8] = 1  # 添加障碍物
    grid_map[10, 5:15] = 1

    start = (1, 1)
    goal = (18, 18)

    print(f"测试环境: {grid_map.shape}, 起点: {start}, 终点: {goal}")

    # 创建PSO规划器
    params = create_pso_params(
        max_iterations=80,
        num_particles=15,
        w=0.6,
        c1=1.8,
        c2=1.8,
        path_length=8
    )
    planner = PSOPlanner(start, goal, grid_map, params)

    # 执行规划
    result = planner.plan()

    # 输出结果
    print(f"✅ PSO规划完成:")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   时间: {result.computation_time:.3f}s")
    print(f"   迭代次数: {result.iterations}")

    if result.success and result.path:
        print(f"   起点: {result.path[0]}")
        print(f"   终点: {result.path[-1]}")

        # 检查路径有效性
        valid = True
        for point in result.path:
            if not PathPlanningUtils.is_valid_point(point, grid_map):
                valid = False
                break

        print(f"   路径有效性: {'✅ 有效' if valid else '❌ 无效'}")

    return result


if __name__ == "__main__":
    test_pso_planner()
