"""
比较RRT和RRT*算法性能
"""

import numpy as np
import time
import matplotlib
matplotlib.use('Agg')  # 不显示图形
import matplotlib.pyplot as plt

from rrt_planner import RRTPlanner, create_rrt_params
from rrt_star_planner import RRTStarPlanner, create_rrt_star_params
from path_planning_base import PathPlanningUtils, PathPlanningVisualizer


def create_test_environments():
    """创建不同复杂度的测试环境"""
    environments = {}
    
    # 简单环境
    simple_map = np.zeros((15, 15))
    simple_map[6:9, 6] = 1  # 小障碍物
    environments['simple'] = {
        'map': simple_map,
        'start': (2, 2),
        'goal': (12, 12),
        'description': '简单环境'
    }
    
    # 中等复杂度环境
    medium_map = np.zeros((20, 20))
    medium_map[8:12, 8] = 1    # 垂直墙
    medium_map[10, 5:12] = 1   # 水平墙
    medium_map[3:6, 3:6] = 1   # 小方块
    medium_map[15:18, 15:18] = 1  # 另一个方块
    environments['medium'] = {
        'map': medium_map,
        'start': (1, 1),
        'goal': (18, 18),
        'description': '中等复杂度环境'
    }
    
    # 复杂环境
    complex_map = np.zeros((25, 25))
    # 创建迷宫样的结构
    complex_map[5:20, 5] = 1   # 左墙
    complex_map[5:20, 19] = 1  # 右墙
    complex_map[5, 5:20] = 1   # 上墙
    complex_map[19, 5:20] = 1  # 下墙
    complex_map[10:15, 10] = 1 # 内部障碍
    complex_map[12, 10:15] = 1 # 内部障碍
    # 留出通道
    complex_map[7, 5] = 0      # 入口
    complex_map[17, 19] = 0    # 出口
    complex_map[12, 12] = 0    # 内部通道
    environments['complex'] = {
        'map': complex_map,
        'start': (2, 7),
        'goal': (22, 17),
        'description': '复杂环境'
    }
    
    return environments


def run_algorithm_comparison(env_name, env_data, num_runs=5):
    """运行算法比较"""
    print(f"\n🧪 测试环境: {env_data['description']} ({env_name})")
    print(f"地图大小: {env_data['map'].shape}")
    print(f"起点: {env_data['start']}, 终点: {env_data['goal']}")
    print(f"障碍物数量: {np.sum(env_data['map'])}")
    
    results = {
        'RRT': [],
        'RRT*': []
    }
    
    # 创建算法参数
    rrt_params = create_rrt_params(
        max_iterations=1000,
        step_size=1.0,
        goal_bias=0.1,
        timeout=10.0
    )
    
    rrt_star_params = create_rrt_star_params(
        max_iterations=1000,
        step_size=1.0,
        goal_bias=0.1,
        timeout=10.0,
        initial_radius=2.5,
        min_radius=1.0
    )
    
    # 运行多次测试
    for run in range(num_runs):
        print(f"  运行 {run + 1}/{num_runs}...")
        
        # 测试RRT
        rrt_planner = RRTPlanner(
            env_data['start'], env_data['goal'], env_data['map'], rrt_params
        )
        rrt_result = rrt_planner.plan()
        results['RRT'].append(rrt_result)
        
        # 测试RRT*
        rrt_star_planner = RRTStarPlanner(
            env_data['start'], env_data['goal'], env_data['map'], rrt_star_params
        )
        rrt_star_result = rrt_star_planner.plan()
        results['RRT*'].append(rrt_star_result)
    
    return results


def analyze_results(results):
    """分析结果"""
    analysis = {}
    
    for algorithm, result_list in results.items():
        # 计算统计数据
        success_count = sum(1 for r in result_list if r.success)
        success_rate = success_count / len(result_list)
        
        # 只分析成功的结果
        successful_results = [r for r in result_list if r.success]
        
        if successful_results:
            costs = [r.cost for r in successful_results]
            times = [r.computation_time for r in successful_results]
            iterations = [r.iterations for r in successful_results]
            path_lengths = [len(r.path) for r in successful_results]
            
            # 计算路径质量指标
            path_metrics = []
            for r in successful_results:
                if r.path:
                    metrics = PathPlanningUtils.calculate_path_metrics(r.path)
                    path_metrics.append(metrics)
            
            euclidean_lengths = [m['euclidean_length'] for m in path_metrics]
            smoothness_values = [m['smoothness'] for m in path_metrics]
            turn_counts = [m['turn_count'] for m in path_metrics]
            
            analysis[algorithm] = {
                'success_rate': success_rate,
                'avg_cost': np.mean(costs),
                'std_cost': np.std(costs),
                'avg_time': np.mean(times),
                'std_time': np.std(times),
                'avg_iterations': np.mean(iterations),
                'avg_path_length': np.mean(path_lengths),
                'avg_euclidean_length': np.mean(euclidean_lengths) if euclidean_lengths else 0,
                'avg_smoothness': np.mean(smoothness_values) if smoothness_values else 0,
                'avg_turn_count': np.mean(turn_counts) if turn_counts else 0,
                'successful_runs': len(successful_results),
                'total_runs': len(result_list)
            }
        else:
            analysis[algorithm] = {
                'success_rate': 0,
                'successful_runs': 0,
                'total_runs': len(result_list)
            }
    
    return analysis


def print_analysis(env_name, analysis):
    """打印分析结果"""
    print(f"\n📊 {env_name} 环境分析结果:")
    print("=" * 60)
    
    for algorithm, stats in analysis.items():
        print(f"\n{algorithm} 算法:")
        print(f"  成功率: {stats['success_rate']:.2%} ({stats['successful_runs']}/{stats['total_runs']})")
        
        if stats['successful_runs'] > 0:
            print(f"  平均代价: {stats['avg_cost']:.2f} ± {stats['std_cost']:.2f}")
            print(f"  平均时间: {stats['avg_time']:.3f}s ± {stats['std_time']:.3f}s")
            print(f"  平均迭代: {stats['avg_iterations']:.0f}")
            print(f"  平均路径长度: {stats['avg_path_length']:.1f}")
            print(f"  平均欧几里得长度: {stats['avg_euclidean_length']:.2f}")
            print(f"  平均平滑度: {stats['avg_smoothness']:.2f}")
            print(f"  平均转弯次数: {stats['avg_turn_count']:.1f}")


def compare_algorithms():
    """主比较函数"""
    print("🚀 开始RRT vs RRT*算法性能比较")
    print("=" * 60)
    
    # 创建测试环境
    environments = create_test_environments()
    
    all_results = {}
    all_analysis = {}
    
    # 对每个环境进行测试
    for env_name, env_data in environments.items():
        results = run_algorithm_comparison(env_name, env_data, num_runs=3)
        analysis = analyze_results(results)
        
        all_results[env_name] = results
        all_analysis[env_name] = analysis
        
        print_analysis(env_name, analysis)
    
    # 总结比较
    print("\n🎯 总体比较总结:")
    print("=" * 60)
    
    for env_name in environments.keys():
        analysis = all_analysis[env_name]
        print(f"\n{env_name} 环境:")
        
        rrt_success = analysis['RRT']['success_rate']
        rrt_star_success = analysis['RRT*']['success_rate']
        
        if rrt_success > 0 and rrt_star_success > 0:
            rrt_cost = analysis['RRT']['avg_cost']
            rrt_star_cost = analysis['RRT*']['avg_cost']
            cost_improvement = (rrt_cost - rrt_star_cost) / rrt_cost * 100
            
            rrt_time = analysis['RRT']['avg_time']
            rrt_star_time = analysis['RRT*']['avg_time']
            time_ratio = rrt_star_time / rrt_time
            
            print(f"  RRT*路径质量提升: {cost_improvement:.1f}%")
            print(f"  RRT*计算时间比例: {time_ratio:.2f}x")
            
            if cost_improvement > 5:
                print("  ✅ RRT*在路径质量上有显著优势")
            elif cost_improvement > 0:
                print("  ✅ RRT*在路径质量上略有优势")
            else:
                print("  ⚠️ RRT*在路径质量上无明显优势")
            
            if time_ratio < 2.0:
                print("  ✅ RRT*计算开销可接受")
            else:
                print("  ⚠️ RRT*计算开销较高")
        else:
            print(f"  RRT成功率: {rrt_success:.1%}, RRT*成功率: {rrt_star_success:.1%}")
    
    print("\n🎉 算法比较完成!")
    return all_results, all_analysis


if __name__ == "__main__":
    compare_algorithms()